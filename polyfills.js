// polyfills.js
import { polyfillGlobal } from 'react-native/Libraries/Utilities/PolyfillFunctions';

import { ReadableStream } from "readable-stream";
polyfillGlobal("ReadableStream", () => ReadableStream); // polyfill ReadableStream

import "@azure/core-asynciterator-polyfill"; // polyfill Async Iterator
import "@bacons/text-decoder/install"; // polyfill Text Decoder
import 'react-native-get-random-values'; // polyfill getRandomValues

// Polyfill crypto for @noble/hashes compatibility in React Native
// Note: @noble/hashes should work with react-native-get-random-values
// If issues persist, consider using react-native-crypto or expo-crypto

// Polyfill Buffer if not available
if (typeof global.Buffer === 'undefined') {
  global.Buffer = require('buffer').Buffer;
}

// Ensure crypto.getRandomValues is available (should be provided by react-native-get-random-values)
if (typeof global.crypto === 'undefined') {
  global.crypto = {};
}

// Add a basic crypto.getRandomValues if not available
if (typeof global.crypto.getRandomValues === 'undefined') {
  const { getRandomValues } = require('react-native-get-random-values');
  global.crypto.getRandomValues = getRandomValues;
}
