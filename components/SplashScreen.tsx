import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSpring,
  withSequence,
  runOnJS,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import * as SplashScreen from 'expo-splash-screen';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onAnimationFinish?: () => void;
  loadingProgress?: number;
}

export default function CustomSplashScreen({
  onAnimationFinish,
  loadingProgress = 0
}: SplashScreenProps) {
  // Animation values
  const logoScale = useSharedValue(0.5);
  const logoOpacity = useSharedValue(0);
  const dotScale = useSharedValue(0);
  const dotOpacity = useSharedValue(0);
  const titleOpacity = useSharedValue(0);
  const titleTranslateY = useSharedValue(30);
  const subtitleOpacity = useSharedValue(0);
  const subtitleTranslateY = useSharedValue(20);
  const backgroundOpacity = useSharedValue(0);
  const sparkleRotation = useSharedValue(0);
  const sparkleScale = useSharedValue(0);
  const progressWidth = useSharedValue(0);
  const progressOpacity = useSharedValue(0);

  useEffect(() => {
    // Hide the default splash screen
    SplashScreen.hideAsync();

    // Start animation sequence
    const startAnimation = () => {
      // Background fade in
      backgroundOpacity.value = withTiming(1, { duration: 300 });

      // Logo animation
      logoOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
      logoScale.value = withDelay(200, withSpring(1, { damping: 15, stiffness: 200 }));

      // Dot animation with bounce
      dotOpacity.value = withDelay(600, withTiming(1, { duration: 400 }));
      dotScale.value = withDelay(600, withSequence(
        withSpring(1.3, { damping: 10, stiffness: 300 }),
        withSpring(1, { damping: 15, stiffness: 200 })
      ));

      // Title slide up
      titleOpacity.value = withDelay(800, withTiming(1, { duration: 500 }));
      titleTranslateY.value = withDelay(800, withSpring(0, { damping: 15, stiffness: 200 }));

      // Subtitle slide up
      subtitleOpacity.value = withDelay(1000, withTiming(1, { duration: 500 }));
      subtitleTranslateY.value = withDelay(1000, withSpring(0, { damping: 15, stiffness: 200 }));

      // Sparkle animation
      sparkleScale.value = withDelay(1200, withSpring(1, { damping: 12, stiffness: 300 }));
      sparkleRotation.value = withDelay(1200, withTiming(360, { duration: 1000 }));

      // Progress bar animation
      progressOpacity.value = withDelay(1400, withTiming(1, { duration: 300 }));

      // Finish animation after 2.5 seconds
      setTimeout(() => {
        if (onAnimationFinish) {
          onAnimationFinish();
        }
      }, 2500);
    };

    startAnimation();
  }, []);

  // Update progress bar based on loading progress
  useEffect(() => {
    progressWidth.value = withTiming((loadingProgress / 100) * 200, { duration: 300 });
  }, [loadingProgress]);

  // Animated styles
  const backgroundAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backgroundOpacity.value,
  }));

  const logoAnimatedStyle = useAnimatedStyle(() => ({
    opacity: logoOpacity.value,
    transform: [{ scale: logoScale.value }],
  }));

  const dotAnimatedStyle = useAnimatedStyle(() => ({
    opacity: dotOpacity.value,
    transform: [{ scale: dotScale.value }],
  }));

  const titleAnimatedStyle = useAnimatedStyle(() => ({
    opacity: titleOpacity.value,
    transform: [{ translateY: titleTranslateY.value }],
  }));

  const subtitleAnimatedStyle = useAnimatedStyle(() => ({
    opacity: subtitleOpacity.value,
    transform: [{ translateY: subtitleTranslateY.value }],
  }));

  const sparkleAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: sparkleScale.value },
      { rotate: `${sparkleRotation.value}deg` },
    ],
  }));

  const progressBarAnimatedStyle = useAnimatedStyle(() => ({
    opacity: progressOpacity.value,
  }));

  const progressFillAnimatedStyle = useAnimatedStyle(() => ({
    width: progressWidth.value,
  }));

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.background, backgroundAnimatedStyle]} />
      
      {/* Sparkle decorations */}
      <Animated.View style={[styles.sparkle, styles.sparkle1, sparkleAnimatedStyle]}>
        <Ionicons name="sparkles" size={16} color="#0099FF" />
      </Animated.View>
      <Animated.View style={[styles.sparkle, styles.sparkle2, sparkleAnimatedStyle]}>
        <Ionicons name="sparkles" size={12} color="#00C896" />
      </Animated.View>
      <Animated.View style={[styles.sparkle, styles.sparkle3, sparkleAnimatedStyle]}>
        <Ionicons name="sparkles" size={14} color="#9B59FF" />
      </Animated.View>

      {/* Main content */}
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <Animated.Text style={[styles.logoText, logoAnimatedStyle]}>
            ALIAS
          </Animated.Text>
          <Animated.View style={[styles.logoDot, dotAnimatedStyle]} />
        </View>

        <Animated.Text style={[styles.title, titleAnimatedStyle]}>
          Component Gallery
        </Animated.Text>

        <Animated.Text style={[styles.subtitle, subtitleAnimatedStyle]}>
          AI-Powered Development
        </Animated.Text>

        {/* Progress Bar */}
        <Animated.View style={[styles.progressContainer, progressBarAnimatedStyle]}>
          <View style={styles.progressBar}>
            <Animated.View style={[styles.progressFill, progressFillAnimatedStyle]} />
          </View>
          <Text style={styles.progressText}>{Math.round(loadingProgress)}%</Text>
        </Animated.View>
      </View>

      {/* Bottom branding */}
      <View style={styles.bottomBranding}>
        <Text style={styles.brandingText}>Powered by ALIAS</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000000',
  },
  content: {
    alignItems: 'center',
    gap: 16,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 48,
    fontWeight: '700',
    letterSpacing: 3,
  },
  logoDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#0099FF',
    ...Platform.select({
      ios: {
        shadowColor: '#0099FF',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.8,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  title: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '300',
    letterSpacing: 1,
    textAlign: 'center',
  },
  subtitle: {
    color: '#9CA3AF',
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 2,
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  progressContainer: {
    alignItems: 'center',
    gap: 8,
    marginTop: 24,
  },
  progressBar: {
    width: 200,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#0099FF',
    borderRadius: 2,
  },
  progressText: {
    color: '#6B7280',
    fontSize: 10,
    fontWeight: '500',
    letterSpacing: 1,
  },
  sparkle: {
    position: 'absolute',
  },
  sparkle1: {
    top: height * 0.25,
    left: width * 0.2,
  },
  sparkle2: {
    top: height * 0.35,
    right: width * 0.25,
  },
  sparkle3: {
    bottom: height * 0.3,
    left: width * 0.15,
  },
  bottomBranding: {
    position: 'absolute',
    bottom: 60,
    alignItems: 'center',
  },
  brandingText: {
    color: '#6B7280',
    fontSize: 12,
    fontWeight: '500',
    letterSpacing: 1,
  },
});
