import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useJazzAuth, createJazzProfile } from '@/lib/jazz/provider';
import LoadingSpinner from './LoadingSpinner';

interface JazzAuthProps {
  onAuthComplete?: () => void;
}

export default function JazzAuth({ onAuthComplete }: JazzAuthProps) {
  const { isAuthenticated, account, isLoading } = useJazzAuth();
  const [profileData, setProfileData] = useState({
    name: '',
    bio: '',
  });
  const [isCreatingProfile, setIsCreatingProfile] = useState(false);

  // If already authenticated, show success
  if (isAuthenticated && account?.profile) {
    return (
      <View style={styles.successContainer}>
        <Ionicons name="checkmark-circle" size={48} color="#00C896" />
        <Text style={styles.successTitle}>Jazz Connected!</Text>
        <Text style={styles.successMessage}>
          Real-time collaboration is now enabled
        </Text>
        {onAuthComplete && (
          <TouchableOpacity style={styles.continueButton} onPress={onAuthComplete}>
            <Text style={styles.continueButtonText}>Continue</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  // If account exists but no profile, show profile creation
  if (isAuthenticated && account && !account.profile) {
    const handleCreateProfile = async () => {
      if (!profileData.name.trim()) {
        Alert.alert('Error', 'Please enter your name');
        return;
      }

      setIsCreatingProfile(true);
      try {
        await createJazzProfile(account, {
          name: profileData.name.trim(),
          bio: profileData.bio.trim() || undefined,
        });
        
        if (onAuthComplete) {
          onAuthComplete();
        }
      } catch (error) {
        console.error('Failed to create profile:', error);
        Alert.alert('Error', 'Failed to create profile. Please try again.');
      } finally {
        setIsCreatingProfile(false);
      }
    };

    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Ionicons name="person-add" size={32} color="#0099FF" />
          <Text style={styles.title}>Complete Your Profile</Text>
          <Text style={styles.subtitle}>
            Set up your Jazz profile for collaboration
          </Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Display Name *</Text>
            <TextInput
              style={styles.input}
              value={profileData.name}
              onChangeText={(text) => setProfileData(prev => ({ ...prev, name: text }))}
              placeholder="Enter your name"
              placeholderTextColor="#6B7280"
              autoCapitalize="words"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Bio (Optional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={profileData.bio}
              onChangeText={(text) => setProfileData(prev => ({ ...prev, bio: text }))}
              placeholder="Tell others about yourself"
              placeholderTextColor="#6B7280"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <TouchableOpacity
            style={[styles.createButton, isCreatingProfile && styles.createButtonDisabled]}
            onPress={handleCreateProfile}
            disabled={isCreatingProfile}
          >
            {isCreatingProfile ? (
              <LoadingSpinner size={16} color="#FFFFFF" />
            ) : (
              <>
                <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                <Text style={styles.createButtonText}>Create Profile</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner size={32} color="#0099FF" />
        <Text style={styles.loadingText}>Connecting to Jazz...</Text>
      </View>
    );
  }

  // Not authenticated - show connection info
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="flash" size={32} color="#0099FF" />
        <Text style={styles.title}>Enable Real-time Features</Text>
        <Text style={styles.subtitle}>
          Connect to Jazz for instant collaboration, offline sync, and real-time updates
        </Text>
      </View>

      <View style={styles.features}>
        <View style={styles.feature}>
          <Ionicons name="people" size={20} color="#0099FF" />
          <Text style={styles.featureText}>Real-time collaboration</Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="cloud-offline" size={20} color="#0099FF" />
          <Text style={styles.featureText}>Offline-first sync</Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="chatbubbles" size={20} color="#0099FF" />
          <Text style={styles.featureText}>Live comments & presence</Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="shield-checkmark" size={20} color="#0099FF" />
          <Text style={styles.featureText}>End-to-end encryption</Text>
        </View>
      </View>

      <Text style={styles.note}>
        Jazz will automatically connect when you start collaborating
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 24,
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  form: {
    width: '100%',
    maxWidth: 400,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    color: '#FFFFFF',
    fontSize: 16,
  },
  textArea: {
    height: 80,
    paddingTop: 16,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#0099FF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginTop: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#0099FF',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  createButtonDisabled: {
    opacity: 0.6,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  features: {
    gap: 16,
    marginBottom: 24,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  note: {
    color: '#6B7280',
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    color: '#9CA3AF',
    fontSize: 14,
  },
  successContainer: {
    padding: 24,
    alignItems: 'center',
    gap: 16,
  },
  successTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  successMessage: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center',
  },
  continueButton: {
    backgroundColor: '#0099FF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginTop: 8,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});
