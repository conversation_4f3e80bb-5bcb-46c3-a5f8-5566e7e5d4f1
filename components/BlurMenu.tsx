import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import React from 'react';
import {
    Dimensions,
    Modal,
    Platform,
    Pressable,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import Animated, {
    runOnJS,
    useAnimatedStyle,
    useSharedValue,
    withDelay,
    withSpring,
    withTiming
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get('window');

// Global Modal Manager to ensure only one BlurMenu modal is active at a time
class BlurMenuModalManager {
    private static instance: BlurMenuModalManager;
    private activeModalId: string | null = null;
    private modals: Map<string, () => void> = new Map();

    static getInstance(): BlurMenuModalManager {
        if (!BlurMenuModalManager.instance) {
            BlurMenuModalManager.instance = new BlurMenuModalManager();
        }
        return BlurMenuModalManager.instance;
    }

    registerModal(id: string, closeCallback: () => void) {
        this.modals.set(id, closeCallback);
    }

    unregisterModal(id: string) {
        this.modals.delete(id);
        if (this.activeModalId === id) {
            this.activeModalId = null;
        }
    }

    requestModalOpen(id: string): boolean {
        // Close any other active modal first
        if (this.activeModalId && this.activeModalId !== id) {
            const closeCallback = this.modals.get(this.activeModalId);
            if (closeCallback) {
                closeCallback();
            }
        }

        this.activeModalId = id;
        return true;
    }

    requestModalClose(id: string) {
        if (this.activeModalId === id) {
            this.activeModalId = null;
        }
    }

    isModalActive(id: string): boolean {
        return this.activeModalId === id;
    }
}

export interface MenuItem {
    id?: string;
    icon?: React.ComponentType<any> | React.ReactNode | keyof typeof Ionicons.glyphMap;
    title: string;
    onPress?: () => void;
}

export type MenuPosition = 'left' | 'right' | 'center';
export type FabPosition = 'top-left' | 'top-right' | 'top-center' | 'bottom-left' | 'bottom-right' | 'bottom-center' | 'left' | 'right' | 'center';

const SPRING_CONFIG = {
    damping: 125,
    stiffness: 300,
    mass: 1,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 2,
} as const;

interface BlurMenuProps {
    visible: boolean;
    onToggle: () => void;
    menuItems: MenuItem[];
    position?: MenuPosition;
    title?: string;
    fabPosition?: FabPosition;
    children?: any;
    className?: string;
}

// Cross-platform blur background component
const BlurBackground: React.FC<{ style?: any }> = ({ style }) => {
    if (Platform.OS === 'ios') {
        return <BlurView intensity={100} tint="dark" style={[StyleSheet.absoluteFill, style]} />;
    }

    // Android fallback - dark semi-transparent background
    return (
        <View
            style={[
                StyleSheet.absoluteFill,
                { backgroundColor: 'rgba(0, 0, 0, 0.85)' },
                style
            ]}
        />
    );
};

const BlurMenu: React.FC<BlurMenuProps> = ({
    visible,
    onToggle,
    menuItems,
    position = 'center',
    title = 'Menu',
    fabPosition = 'bottom-center',
    children,
    className,
}) => {
    const [isModalVisible, setIsModalVisible] = React.useState(false);
    const { top, bottom } = useSafeAreaInsets();

    // Generate unique ID for BlurMenu instance
    const modalId = React.useMemo(() => Math.random().toString(36).substring(7), []);
    const modalManager = BlurMenuModalManager.getInstance();

    const getInitialPosition = () => {
        // Determine animation direction based on fab position
        if (fabPosition.includes('top')) return -SCREEN_HEIGHT;
        if (fabPosition.includes('bottom')) return SCREEN_HEIGHT;
        if (fabPosition.includes('left')) return -SCREEN_WIDTH;
        if (fabPosition.includes('right')) return SCREEN_WIDTH;
        return -SCREEN_HEIGHT; // default
    };

    const AnimatedPressableButton = Animated.createAnimatedComponent(Pressable);

    const menuTranslateX = useSharedValue(0);
    const menuTranslateY = useSharedValue(getInitialPosition());
    const menuOpacity = useSharedValue(0);
    const blurOpacity = useSharedValue(0);
    const closeButtonOpacity = useSharedValue(0);

    const closeModal = React.useCallback(() => {
        setIsModalVisible(false);
        modalManager.requestModalClose(modalId);
    }, [modalId, modalManager]);

    // Register the modal with the manager
    React.useEffect(() => {
        modalManager.registerModal(modalId, () => {
            if (visible) {
                onToggle();
            }
        });

        return () => {
            modalManager.unregisterModal(modalId);
        };
    }, [modalId, modalManager, onToggle, visible]);

    // Sync modal visibility with parent visible state
    React.useEffect(() => {
        if (visible && !isModalVisible) {
            // Request permission to open modal
            modalManager.requestModalOpen(modalId);
            setIsModalVisible(true);
        } else if (!visible && isModalVisible) {
            closeModal();
        }
    }, [visible, isModalVisible, modalId, modalManager, closeModal]);

    React.useEffect(() => {
        if (visible) {
            // Reset positions and show
            blurOpacity.value = withTiming(1, { duration: 200 });

            // Reset positions based on fab position
            if (fabPosition.includes('left')) {
                menuTranslateX.value = withSpring(0, SPRING_CONFIG);
                menuTranslateY.value = 0;
            } else if (fabPosition.includes('right')) {
                menuTranslateX.value = withSpring(0, SPRING_CONFIG);
                menuTranslateY.value = 0;
            } else {
                menuTranslateY.value = withSpring(0, SPRING_CONFIG);
                menuTranslateX.value = 0;
            }

            menuOpacity.value = withTiming(1, { duration: 300 });
            closeButtonOpacity.value = withDelay(200, withTiming(1, { duration: 200 }));
        } else if (isModalVisible) {
            // Hide animation
            closeButtonOpacity.value = withTiming(0, { duration: 100 });
            blurOpacity.value = withTiming(0, { duration: 300 });
            menuOpacity.value = withTiming(0, { duration: 200 });

            const finalPosition = getInitialPosition();
            if (fabPosition.includes('left') || fabPosition.includes('right')) {
                menuTranslateX.value = withSpring(finalPosition, SPRING_CONFIG, (finished) => {
                    if (finished) runOnJS(closeModal)();
                });
            } else {
                menuTranslateY.value = withSpring(finalPosition, SPRING_CONFIG, (finished) => {
                    if (finished) runOnJS(closeModal)();
                });
            }
        }
    }, [visible, fabPosition, isModalVisible, closeModal]);

    const menuAnimatedStyle = useAnimatedStyle(() => {
        'worklet';
        return {
            transform: [
                { translateX: menuTranslateX.value },
                { translateY: menuTranslateY.value }
            ] as any,
            opacity: menuOpacity.value,
        };
    });

    const blurAnimatedStyle = useAnimatedStyle(() => {
        'worklet';
        return {
            opacity: blurOpacity.value,
        };
    });

    const closeButtonAnimatedStyle = useAnimatedStyle(() => {
        'worklet';
        return {
            opacity: closeButtonOpacity.value,
        };
    });

    const getFabContainerStyle = () => {
        const baseStyle = [styles.fabContainer];

        switch (fabPosition) {
            case 'top-left':
                return [...baseStyle, styles.fabTopLeft];
            case 'top-right':
                return [...baseStyle, styles.fabTopRight];
            case 'top-center':
                return [...baseStyle, styles.fabTopCenter];
            case 'bottom-left':
                return [...baseStyle, styles.fabBottomLeft];
            case 'bottom-right':
                return [...baseStyle, styles.fabBottomRight];
            case 'bottom-center':
                return [...baseStyle, styles.fabBottomCenter];
            case 'left':
                return [...baseStyle, styles.fabLeft];
            case 'right':
                return [...baseStyle, styles.fabRight];
            case 'center':
                return [...baseStyle, styles.fabCenter];
            default:
                return [...baseStyle, styles.fabBottomCenter];
        }
    };

    const getMenuAlignment = () => {
        if (fabPosition.includes('left')) return 'flex-start';
        if (fabPosition.includes('right')) return 'flex-end';
        return 'center';
    };

    const getMenuTextAlign = (): 'left' | 'right' | 'center' => {
        if (fabPosition.includes('left')) return 'left';
        if (fabPosition.includes('right')) return 'right';
        return 'center';
    };

    return (
        <>
            {/* The Trigger */}
            {children ? (
                <TouchableOpacity
                    className={className}
                    activeOpacity={0.8}
                    onPress={onToggle}
                    hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                    style={{ zIndex: 1 }}>
                    {children}
                </TouchableOpacity>
            ) : (
                <View style={getFabContainerStyle()}>
                    <TouchableOpacity
                        style={styles.fab}
                        onPress={onToggle}
                    >
                        <Text>Menu</Text>
                    </TouchableOpacity>
                </View>
            )}

            <Modal
                visible={isModalVisible && modalManager.isModalActive(modalId)}
                transparent={true}
                animationType="none"
                statusBarTranslucent={true}
            >
                {/* Full Screen Blur Background - This will catch all touches */}
                <AnimatedPressableButton
                    onPress={onToggle}
                    style={[styles.fullScreenTouchable, blurAnimatedStyle]}
                >
                    <BlurBackground />
                </AnimatedPressableButton>

                {/* Close Button inside Modal */}
                <Animated.View style={[getFabContainerStyle(), closeButtonAnimatedStyle]}>
                    <TouchableOpacity
                        style={[styles.fab]}
                        onPress={onToggle}
                    >
                        <Ionicons name="close" size={20} color="#000" />
                    </TouchableOpacity>
                </Animated.View>

                {/* Menu Container */}
                <Animated.View
                    style={[
                        styles.menuContainer,
                        {
                            paddingTop: fabPosition.includes('top') ? top + 80 : top + 40,
                            paddingBottom: fabPosition.includes('bottom') ? bottom + 80 : bottom + 40,
                        },
                        menuAnimatedStyle
                    ]}
                    pointerEvents="box-none"
                >
                    <View
                        style={[styles.menuHeader, {
                            alignItems: getMenuAlignment(),
                        }]}
                        pointerEvents="none"
                    >
                        <Text style={[styles.menuTitle, {
                            fontSize: 32,
                            textAlign: getMenuTextAlign(),
                        }]}>{title}</Text>
                    </View>

                    <View style={styles.menuContent} pointerEvents="box-none">
                        {menuItems.map((item, index) => (
                            <MenuItemComponent
                                key={item.id || index}
                                item={item}
                                index={index}
                                visible={visible}
                                fabPosition={fabPosition}
                                onToggle={onToggle}
                            />
                        ))}
                    </View>
                </Animated.View>
            </Modal>
        </>
    );
};

const MenuItemComponent: React.FC<{
    item: MenuItem;
    index: number;
    visible: boolean;
    fabPosition: FabPosition;
    onToggle: () => void;
}> = ({ item, index, visible, fabPosition, onToggle }) => {
    // Initialize shared values with proper initial positions
    const getInitialX = () => {
        if (fabPosition.includes('left')) return -100;
        if (fabPosition.includes('right')) return 100;
        return 0;
    };

    const getInitialY = () => {
        if (fabPosition.includes('top')) return -100;
        if (fabPosition.includes('bottom')) return 100;
        return 0;
    };

    const translateX = useSharedValue(getInitialX());
    const translateY = useSharedValue(getInitialY());
    const opacity = useSharedValue(0);

    React.useEffect(() => {
        if (visible) {
            // Staggered entrance with spring
            translateX.value = withDelay(index * 50, withSpring(0, SPRING_CONFIG));
            translateY.value = withDelay(index * 50, withSpring(0, SPRING_CONFIG));
            opacity.value = withDelay(index * 50, withTiming(1, { duration: 300 }));
        } else {
            // Quick exit
            const exitOffset = 100;
            if (fabPosition.includes('left')) {
                translateX.value = withTiming(-exitOffset, { duration: 200 });
            } else if (fabPosition.includes('right')) {
                translateX.value = withTiming(exitOffset, { duration: 200 });
            }

            if (fabPosition.includes('top')) {
                translateY.value = withTiming(-exitOffset, { duration: 200 });
            } else if (fabPosition.includes('bottom')) {
                translateY.value = withTiming(exitOffset, { duration: 200 });
            }

            opacity.value = withTiming(0, { duration: 200 });
        }
    }, [visible, index, fabPosition]);

    const animatedStyle = useAnimatedStyle(() => {
        'worklet';
        return {
            transform: [
                { translateX: translateX.value },
                { translateY: translateY.value }
            ] as any,
            opacity: opacity.value,
        };
    });

    // Improved icon rendering with proper type handling
    const renderIcon = () => {
        if (React.isValidElement(item.icon)) {
            return item.icon;
        } else if (typeof item.icon === 'function') {
            const IconComponent = item.icon as React.ComponentType<any>;
            return <IconComponent size={20} color="#FFFFFF" />;
        } else if (typeof item.icon === 'string') {
            return <Ionicons name={item.icon as keyof typeof Ionicons.glyphMap} size={20} color="#FFFFFF" />;
        }
        return null;
    };

    const handlePress = () => {
        item.onPress?.();
        onToggle();
    };

    return (
        <Animated.View style={[styles.menuItem, animatedStyle]} pointerEvents="auto">
            <TouchableOpacity
                style={styles.menuItemTouchable}
                onPress={handlePress}
            >
                {item.icon && (
                    <View style={styles.menuItemIcon}>
                        {renderIcon()}
                    </View>
                )}
                <Text style={styles.menuItemTitle}>{item.title}</Text>
            </TouchableOpacity>
        </Animated.View>
    );
};

export default BlurMenu;

const styles = StyleSheet.create({
    fullScreenTouchable: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 10,
    },
    fabContainer: {
        position: 'absolute',
        zIndex: 30,
    },
    // Top positions
    fabTopLeft: {
        top: 60,
        left: 20,
    },
    fabTopRight: {
        top: 60,
        right: 20,
    },
    fabTopCenter: {
        top: 60,
        left: '50%',
        marginLeft: -28,
    },
    // Bottom positions
    fabBottomLeft: {
        bottom: 30,
        left: 20,
    },
    fabBottomRight: {
        bottom: 30,
        right: 20,
    },
    fabBottomCenter: {
        bottom: 30,
        left: '50%',
        marginLeft: -28,
    },
    fabLeft: {
        bottom: 30,
        left: 20,
    },
    fabRight: {
        bottom: 30,
        right: 20,
    },
    fabCenter: {
        bottom: 30,
        left: '50%',
        marginLeft: -28, // Half of fab width
    },
    fab: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        backgroundColor: '#FFF',
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    fabActive: {
        backgroundColor: '#FF3B30',
    },
    menuContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 20,
        backgroundColor: 'transparent',
    },
    menuHeader: {
        marginBottom: 40,
        paddingHorizontal: 30
    },
    menuTitle: {
        fontSize: 28,
        fontWeight: '600',
        color: '#FFFFFF',
        textAlign: 'center',
    },
    menuContent: {
        flex: 1,
        paddingHorizontal: 30,
        paddingBottom: 40,
    },
    menuItem: {
        marginBottom: 4,
    },
    menuItemTouchable: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 20,
        paddingHorizontal: 4,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    },
    menuItemIcon: {
        width: 20,
        height: 20,
        marginRight: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    menuItemTitle: {
        fontSize: 18,
        fontWeight: '400',
        color: '#FFFFFF',
    },
});