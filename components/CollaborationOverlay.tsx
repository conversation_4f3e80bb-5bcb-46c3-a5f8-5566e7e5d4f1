import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  PanResponder,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { usePresence } from '@/lib/jazz/hooks';
import { useJazzAuth } from '@/lib/jazz/provider';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface CollaborationOverlayProps {
  componentId?: string;
  enabled?: boolean;
}

interface CursorProps {
  x: number;
  y: number;
  color: string;
  userName: string;
  isActive: boolean;
}

function CollaborativeCursor({ x, y, color, userName, isActive }: CursorProps) {
  const opacity = new Animated.Value(isActive ? 1 : 0);
  const scale = new Animated.Value(isActive ? 1 : 0.8);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: isActive ? 1 : 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scale, {
        toValue: isActive ? 1 : 0.8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isActive]);

  if (!isActive) return null;

  return (
    <Animated.View
      style={[
        styles.cursor,
        {
          left: x - 6,
          top: y - 6,
          opacity,
          transform: [{ scale }],
        },
      ]}
      pointerEvents="none"
    >
      {/* Cursor pointer */}
      <View style={[styles.cursorPointer, { backgroundColor: color }]} />
      
      {/* User name label */}
      <View style={[styles.cursorLabel, { backgroundColor: color }]}>
        <Text style={styles.cursorLabelText}>{userName}</Text>
      </View>
    </Animated.View>
  );
}

export default function CollaborationOverlay({ 
  componentId, 
  enabled = true 
}: CollaborationOverlayProps) {
  const { isAuthenticated } = useJazzAuth();
  const { session, participants, joinSession, updateCursor, leaveSession } = usePresence(componentId);
  const [showParticipants, setShowParticipants] = useState(false);

  // Join session when component mounts and user is authenticated
  useEffect(() => {
    if (isAuthenticated && componentId && enabled) {
      joinSession(componentId);
      
      return () => {
        leaveSession();
      };
    }
  }, [isAuthenticated, componentId, enabled]);

  // Pan responder to track cursor movement
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => false,
    onMoveShouldSetPanResponder: () => false,
    onPanResponderGrant: () => false,
    onPanResponderMove: (evt) => {
      if (enabled && isAuthenticated) {
        const { locationX, locationY } = evt.nativeEvent;
        updateCursor(locationX, locationY);
      }
    },
  });

  if (!enabled || !isAuthenticated || !componentId) {
    return null;
  }

  const activeParticipants = participants.filter(p => p.isActive && p.userId !== session?.participants.find(sp => sp.userId)?.userId);
  const participantCount = activeParticipants.length;

  return (
    <View style={styles.overlay} {...panResponder.panHandlers}>
      {/* Render other users' cursors */}
      {activeParticipants.map((participant) => (
        participant.cursor && (
          <CollaborativeCursor
            key={participant.userId}
            x={participant.cursor.x}
            y={participant.cursor.y}
            color={participant.color}
            userName={participant.userName}
            isActive={participant.isActive}
          />
        )
      ))}

      {/* Collaboration indicator */}
      {participantCount > 0 && (
        <View style={styles.collaborationIndicator}>
          <TouchableOpacity
            style={styles.participantButton}
            onPress={() => setShowParticipants(!showParticipants)}
          >
            <View style={styles.participantAvatars}>
              {activeParticipants.slice(0, 3).map((participant, index) => (
                <View
                  key={participant.userId}
                  style={[
                    styles.participantAvatar,
                    { 
                      backgroundColor: participant.color,
                      marginLeft: index > 0 ? -8 : 0,
                      zIndex: 3 - index,
                    },
                  ]}
                >
                  <Text style={styles.participantInitial}>
                    {participant.userName.charAt(0).toUpperCase()}
                  </Text>
                </View>
              ))}
              {participantCount > 3 && (
                <View style={[styles.participantAvatar, styles.moreParticipants]}>
                  <Text style={styles.participantInitial}>+{participantCount - 3}</Text>
                </View>
              )}
            </View>
            <Ionicons 
              name={showParticipants ? "chevron-up" : "chevron-down"} 
              size={12} 
              color="#FFFFFF" 
            />
          </TouchableOpacity>

          {/* Participants list */}
          {showParticipants && (
            <View style={styles.participantsList}>
              <Text style={styles.participantsTitle}>Active Collaborators</Text>
              {activeParticipants.map((participant) => (
                <View key={participant.userId} style={styles.participantItem}>
                  <View style={[styles.participantDot, { backgroundColor: participant.color }]} />
                  <Text style={styles.participantName}>{participant.userName}</Text>
                  <View style={styles.participantStatus}>
                    <View style={styles.onlineIndicator} />
                    <Text style={styles.statusText}>Online</Text>
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      )}

      {/* Live indicator */}
      {participantCount > 0 && (
        <View style={styles.liveIndicator}>
          <View style={styles.liveDot} />
          <Text style={styles.liveText}>LIVE</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'box-none',
  },
  cursor: {
    position: 'absolute',
    pointerEvents: 'none',
  },
  cursorPointer: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  cursorLabel: {
    position: 'absolute',
    top: 16,
    left: 0,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    minWidth: 40,
  },
  cursorLabelText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
    textAlign: 'center',
  },
  collaborationIndicator: {
    position: 'absolute',
    top: 60,
    right: 20,
    zIndex: 1000,
  },
  participantButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  participantAvatars: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  moreParticipants: {
    backgroundColor: '#6B7280',
  },
  participantInitial: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  participantsList: {
    position: 'absolute',
    top: 40,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: 12,
    padding: 16,
    minWidth: 200,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  participantsTitle: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 12,
  },
  participantItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 4,
  },
  participantDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  participantName: {
    color: '#FFFFFF',
    fontSize: 12,
    flex: 1,
  },
  participantStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  onlineIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#00C896',
  },
  statusText: {
    color: '#00C896',
    fontSize: 10,
    fontWeight: '500',
  },
  liveIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 0, 0, 0.3)',
  },
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF0000',
  },
  liveText: {
    color: '#FF0000',
    fontSize: 10,
    fontWeight: '600',
    letterSpacing: 1,
  },
});
