import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  Platform,
  Animated,
  KeyboardAvoidingView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { useComments } from '@/lib/jazz/hooks';
import { useJazzAuth, formatJazzTimestamp, getUserPresenceColor } from '@/lib/jazz/provider';

interface CommentsSectionProps {
  componentId: string;
  isVisible: boolean;
  onClose: () => void;
}

interface CommentItemProps {
  comment: any;
  onReply: (commentId: string) => void;
  onLike: (commentId: string) => void;
  depth?: number;
}

function CommentItem({ comment, onReply, onLike, depth = 0 }: CommentItemProps) {
  const { account } = useJazzAuth();
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleLike = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onLike(comment.id);
  };

  const handleReply = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    onReply(comment.id);
  };

  const isLiked = comment.likedBy?.includes(account?.id);
  const userColor = getUserPresenceColor(comment.author);

  return (
    <Animated.View 
      style={[
        styles.commentItem,
        { 
          marginLeft: depth * 20,
          transform: [
            { translateX: slideAnim.interpolate({ inputRange: [0, 1], outputRange: [50, 0] }) },
            { scale: scaleAnim }
          ],
          opacity: slideAnim,
        }
      ]}
    >
      <View style={styles.commentHeader}>
        <View style={[styles.authorAvatar, { backgroundColor: userColor }]}>
          <Text style={styles.avatarText}>
            {comment.authorName?.charAt(0)?.toUpperCase() || '?'}
          </Text>
        </View>
        <View style={styles.commentMeta}>
          <Text style={styles.authorName}>{comment.authorName}</Text>
          <Text style={styles.timestamp}>
            {formatJazzTimestamp(comment.createdAt)}
            {comment.isEdited && <Text style={styles.editedLabel}> • edited</Text>}
          </Text>
        </View>
      </View>
      
      <Text style={styles.commentContent}>{comment.content}</Text>
      
      <View style={styles.commentActions}>
        <TouchableOpacity 
          style={[styles.actionButton, isLiked && styles.likedButton]}
          onPress={handleLike}
        >
          <Ionicons 
            name={isLiked ? "heart" : "heart-outline"} 
            size={16} 
            color={isLiked ? "#FF4081" : "#9CA3AF"} 
          />
          <Text style={[styles.actionText, isLiked && styles.likedText]}>
            {comment.likes || 0}
          </Text>
        </TouchableOpacity>
        
        {depth < 2 && (
          <TouchableOpacity style={styles.actionButton} onPress={handleReply}>
            <Ionicons name="chatbubble-outline" size={16} color="#9CA3AF" />
            <Text style={styles.actionText}>Reply</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
}

export default function CommentsSection({ componentId, isVisible, onClose }: CommentsSectionProps) {
  const { comments, addComment, likeComment, isLoading } = useComments(componentId);
  const { isAuthenticated, account } = useJazzAuth();
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (isVisible) {
      Animated.spring(slideAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  const handleSubmitComment = async () => {
    if (!newComment.trim() || !isAuthenticated || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await addComment({
        content: newComment.trim(),
        parentId: replyingTo,
      });
      
      setNewComment('');
      setReplyingTo(null);
      
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error('Failed to add comment:', error);
      Alert.alert('Error', 'Failed to add comment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReply = (commentId: string) => {
    setReplyingTo(commentId);
    inputRef.current?.focus();
  };

  const handleLike = async (commentId: string) => {
    try {
      await likeComment(commentId);
    } catch (error) {
      console.error('Failed to like comment:', error);
    }
  };

  const organizeComments = (comments: any[]) => {
    const topLevel = comments.filter(c => !c.parentId);
    const replies = comments.filter(c => c.parentId);
    
    return topLevel.map(comment => ({
      ...comment,
      replies: replies.filter(r => r.parentId === comment.id),
    }));
  };

  const organizedComments = organizeComments(comments);
  const replyingToComment = comments.find(c => c.id === replyingTo);

  if (!isVisible) return null;

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          transform: [
            { translateY: slideAnim.interpolate({ inputRange: [0, 1], outputRange: [300, 0] }) }
          ],
          opacity: slideAnim,
        }
      ]}
    >
      <BlurView intensity={95} style={styles.blurContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>Comments</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <KeyboardAvoidingView 
          style={styles.content}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView style={styles.commentsList} showsVerticalScrollIndicator={false}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Loading comments...</Text>
              </View>
            ) : organizedComments.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Ionicons name="chatbubbles-outline" size={48} color="#9CA3AF" />
                <Text style={styles.emptyText}>No comments yet</Text>
                <Text style={styles.emptySubtext}>Be the first to share your thoughts!</Text>
              </View>
            ) : (
              organizedComments.map((comment) => (
                <View key={comment.id}>
                  <CommentItem
                    comment={comment}
                    onReply={handleReply}
                    onLike={handleLike}
                    depth={0}
                  />
                  {comment.replies?.map((reply: any) => (
                    <CommentItem
                      key={reply.id}
                      comment={reply}
                      onReply={handleReply}
                      onLike={handleLike}
                      depth={1}
                    />
                  ))}
                </View>
              ))
            )}
          </ScrollView>

          {isAuthenticated ? (
            <View style={styles.inputContainer}>
              {replyingTo && (
                <View style={styles.replyingToContainer}>
                  <Text style={styles.replyingToText}>
                    Replying to {replyingToComment?.authorName}
                  </Text>
                  <TouchableOpacity onPress={() => setReplyingTo(null)}>
                    <Ionicons name="close" size={16} color="#9CA3AF" />
                  </TouchableOpacity>
                </View>
              )}
              
              <View style={styles.inputRow}>
                <TextInput
                  ref={inputRef}
                  style={styles.textInput}
                  placeholder="Add a comment..."
                  placeholderTextColor="#9CA3AF"
                  value={newComment}
                  onChangeText={setNewComment}
                  multiline
                  maxLength={500}
                />
                <TouchableOpacity
                  style={[
                    styles.sendButton,
                    (!newComment.trim() || isSubmitting) && styles.sendButtonDisabled
                  ]}
                  onPress={handleSubmitComment}
                  disabled={!newComment.trim() || isSubmitting}
                >
                  <Ionicons 
                    name={isSubmitting ? "hourglass" : "send"} 
                    size={20} 
                    color={(!newComment.trim() || isSubmitting) ? "#9CA3AF" : "#0099FF"} 
                  />
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.authPrompt}>
              <Text style={styles.authPromptText}>Sign in to join the conversation</Text>
            </View>
          )}
        </KeyboardAvoidingView>
      </BlurView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%',
    zIndex: 1000,
  },
  blurContainer: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  commentsList: {
    flex: 1,
    padding: 20,
    paddingTop: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    color: '#9CA3AF',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    color: '#9CA3AF',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  commentItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  authorAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  commentMeta: {
    flex: 1,
  },
  authorName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  timestamp: {
    color: '#9CA3AF',
    fontSize: 12,
    marginTop: 2,
  },
  editedLabel: {
    fontStyle: 'italic',
  },
  commentContent: {
    color: '#FFFFFF',
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 12,
  },
  commentActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginRight: 12,
  },
  likedButton: {
    backgroundColor: 'rgba(255, 64, 129, 0.1)',
  },
  actionText: {
    color: '#9CA3AF',
    fontSize: 12,
    marginLeft: 6,
    fontWeight: '500',
  },
  likedText: {
    color: '#FF4081',
  },
  inputContainer: {
    padding: 20,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  replyingToContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 153, 255, 0.1)',
    padding: 8,
    borderRadius: 8,
    marginBottom: 12,
  },
  replyingToText: {
    color: '#0099FF',
    fontSize: 12,
    fontWeight: '500',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: '#FFFFFF',
    fontSize: 15,
    maxHeight: 100,
    marginRight: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 153, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 153, 255, 0.3)',
  },
  sendButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  authPrompt: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  authPromptText: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center',
  },
});
