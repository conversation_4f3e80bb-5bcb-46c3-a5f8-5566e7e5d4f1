import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const list = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("projects").collect();
  },
});

export const getBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .unique();
  },
});

export const getFeatured = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("projects")
      .filter((q) => q.eq(q.field("featured"), true))
      .first();
  },
});

export const updateStatus = mutation({
  args: {
    projectId: v.id("projects"),
    status: v.union(
      v.literal("backlog"),
      v.literal("in_progress"),
      v.literal("review"),
      v.literal("qa"),
      v.literal("done"),
      v.literal("prototype")
    ),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.projectId, {
      status: args.status,
    });
  },
});

export const create = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    slug: v.string(),
    quote: v.optional(v.string()),
    author: v.optional(v.object({
      name: v.string(),
      role: v.string(),
      avatar: v.string(),
    })),
    tags: v.array(v.string()),
    imageUrl: v.string(),
    heroImage: v.string(),
    featured: v.boolean(),
    category: v.string(),
    accentColor: v.string(),
    status: v.union(
      v.literal("backlog"),
      v.literal("in_progress"),
      v.literal("review"),
      v.literal("qa"),
      v.literal("done"),
      v.literal("prototype")
    ),
    metrics: v.array(v.object({
      label: v.string(),
      value: v.string(),
    })),
    startDate: v.string(),
    targetDate: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("projects", args);
  },
});