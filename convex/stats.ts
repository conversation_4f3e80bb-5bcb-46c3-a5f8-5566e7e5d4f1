import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const list = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("stats").collect();
  },
});

export const listHome = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("stats").collect();
  },
});

export const create = mutation({
  args: {
    label: v.string(),
    value: v.string(),
    color: v.string(),
    updated: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("stats", args);
  },
});