import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Helper to check user permissions
export const require = (ctx: any, allowedRoles: string[]) => {
  // For now, we'll simulate a logged-in user
  // In a real app, this would check authentication
  const currentUser = {
    _id: "demo-user" as Id<"users">,
    role: "admin", // Default to admin for demo
    toolboxTier: "enterprise" as const
  };
  
  if (!allowedRoles.includes(currentUser.role)) {
    throw new Error(`Access denied. Required roles: ${allowedRoles.join(", ")}`);
  }
  
  return currentUser;
};

// Helper to check toolbox tier access
export const allowTool = (ctx: any, minTier: string) => {
  const user = require(ctx, ["client"]);
  const tierOrder = ["free", "starter", "pro", "enterprise"];
  const userTierIndex = tierOrder.indexOf(user.toolboxTier || "free");
  const minTierIndex = tierOrder.indexOf(minTier);
  
  if (userTierIndex < minTierIndex) {
    throw new Error(`Upgrade required. Minimum tier: ${minTier}`);
  }
  
  return user;
};

// Get current user (demo implementation)
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    // Demo user - in real app this would check authentication
    return {
      _id: "demo-user" as Id<"users">,
      name: "Dan Humphreys",
      email: "<EMAIL>",
      role: "admin" as const,
      toolboxTier: "enterprise" as const,
      avatar: "https://avatars.githubusercontent.com/u/00000001"
    };
  },
});

// Switch user role (for demo purposes)
export const switchRole = mutation({
  args: { 
    role: v.union(
      v.literal("admin"),
      v.literal("lead"),
      v.literal("consultant"),
      v.literal("client"),
      v.literal("viewer")
    )
  },
  handler: async (ctx, args) => {
    // In demo, we'll just return the new role
    // In real app, this would update the user record
    return { role: args.role };
  },
});