import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Users with roles and subscription tiers
  users: defineTable({
    name: v.string(),
    email: v.string(),
    role: v.union(
      v.literal("admin"),
      v.literal("lead"),
      v.literal("consultant"),
      v.literal("client"),
      v.literal("viewer")
    ),
    toolboxTier: v.optional(v.union(
      v.literal("free"),
      v.literal("starter"),
      v.literal("pro"),
      v.literal("enterprise")
    )),
    toolboxActiveUntil: v.optional(v.string()),
    avatar: v.optional(v.string()),
  }).index("by_email", ["email"]),

  // Projects with enhanced permissions
  projects: defineTable({
    title: v.string(),
    description: v.string(),
    slug: v.string(),
    quote: v.optional(v.string()),
    author: v.optional(v.object({
      name: v.string(),
      role: v.string(),
      avatar: v.string(),
    })),
    tags: v.array(v.string()),
    imageUrl: v.string(),
    heroImage: v.string(),
    featured: v.boolean(),
    category: v.string(),
    accentColor: v.string(),
    status: v.union(
      v.literal("backlog"),
      v.literal("in_progress"),
      v.literal("review"),
      v.literal("qa"),
      v.literal("done"),
      v.literal("prototype")
    ),
    metrics: v.array(v.object({
      label: v.string(),
      value: v.string(),
    })),
    startDate: v.string(),
    targetDate: v.string(),
    visibleTo: v.optional(v.array(v.string())), // User IDs who can see this project
  }).index("by_slug", ["slug"]),

  // Client profiles for intelligence summaries
  clientProfiles: defineTable({
    userId: v.id("users"),
    intelligence: v.string(),
    collectedDocs: v.array(v.string()),
  }).index("by_user", ["userId"]),

  // Invoices for client billing
  invoices: defineTable({
    userId: v.id("users"),
    stripeId: v.string(),
    pdf: v.optional(v.string()),
    amount: v.number(),
    currency: v.string(),
    periodStart: v.string(),
    periodEnd: v.string(),
    status: v.union(
      v.literal("paid"),
      v.literal("pending"),
      v.literal("overdue")
    ),
  }).index("by_user", ["userId"]),

  // Client roadmaps
  roadmaps: defineTable({
    userId: v.id("users"),
    items: v.array(v.object({
      title: v.string(),
      status: v.string(),
      due: v.optional(v.string()),
      description: v.optional(v.string()),
    })),
  }).index("by_user", ["userId"]),

  // Home page stats
  stats: defineTable({
    label: v.string(),
    value: v.string(),
    color: v.string(),
    updated: v.string(),
  }),

  // Activity feed
  activity: defineTable({
    text: v.string(),
    icon: v.string(),
    ts: v.string(),
  }),

  // Learning courses
  courses: defineTable({
    title: v.string(),
    description: v.string(),
    category: v.string(),
    videoUrl: v.optional(v.string()),
    notes: v.optional(v.string()),
    duration: v.optional(v.string()),
  }),

  // Course progress tracking
  courseProgress: defineTable({
    userId: v.id("users"),
    courseId: v.id("courses"),
    completed: v.boolean(),
    completedAt: v.optional(v.string()),
  }).index("by_user_course", ["userId", "courseId"]),

  // Ops issues (Kanban board)
  issues: defineTable({
    title: v.string(),
    description: v.optional(v.string()),
    status: v.union(
      v.literal("todo"),
      v.literal("in_progress"),
      v.literal("review"),
      v.literal("done")
    ),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    ),
    assignee: v.optional(v.id("users")),
    clientId: v.optional(v.id("users")),
  }),
});