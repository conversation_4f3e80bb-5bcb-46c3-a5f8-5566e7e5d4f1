import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const listRecent = query({
  args: {},
  handler: async (ctx) => {
    // Get recent activity from database, or return mock data if empty
    const activities = await ctx.db.query("activity").order("desc").take(10);

    if (activities.length === 0) {
      // Return mock activity data for demo
      return [
        {
          _id: "1" as any,
          text: "🎨 Glass navigation bar implemented with semantic colors",
          icon: "sparkles",
          ts: new Date().toISOString(),
        },
        {
          _id: "2" as any,
          text: "🚀 ALIAS Component Gallery project created",
          icon: "rocket",
          ts: new Date(Date.now() - 3600000).toISOString(),
        },
        {
          _id: "3" as any,
          text: "✅ Convex backend successfully deployed",
          icon: "checkmark-circle",
          ts: new Date(Date.now() - 7200000).toISOString(),
        }
      ];
    }

    return activities;
  },
});

export const create = mutation({
  args: {
    text: v.string(),
    icon: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("activity", {
      text: args.text,
      icon: args.icon,
      ts: new Date().toISOString(),
    });
  },
});
