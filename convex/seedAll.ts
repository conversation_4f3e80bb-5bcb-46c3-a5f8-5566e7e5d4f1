import { mutation } from "./_generated/server";

export const seedAll = mutation({
  args: {},
  handler: async (ctx) => {
    /* ──────────────────────────────────────────────
       1.  Wipe existing seed data
    ────────────────────────────────────────────── */
    const oldProjects = await ctx.db.query("projects").collect();
    for (const p of oldProjects) await ctx.db.delete(p._id);

    const oldStats = await ctx.db.query("stats").collect();
    for (const s of oldStats) await ctx.db.delete(s._id);

    /* ──────────────────────────────────────────────
       2.  Insert ALIAS initiatives
    ────────────────────────────────────────────── */

    await ctx.db.insert("projects", {
      title: "ALIAS HQ Portal",
      slug: "alias-hq",
      description:
        "Unified command centre for consultants, learning hub, PLF, and client ops.",
      quote:
        "Friction-free orchestration is the difference between motion and momentum.",
      author: {
        name: "Dan Humphreys",
        role: "Founder · CEO",
        avatar:
          "https://avatars.githubusercontent.com/u/00000001", // replace with real
      },
      tags: ["Next.js 15", "Convex", "Expo", "Voice"],
      imageUrl: "https://cdn.alias.ai/projects/alias-hq.png",
      heroImage: "https://cdn.alias.ai/projects/alias-hq.png",
      featured: true,
      category: "platform",
      accentColor: "#0099FF",
      status: "in_progress",
      metrics: [
        { label: "Active consultants", value: "24" },
        { label: "Courses", value: "17" },
        { label: "Automations", value: "38" },
      ],
      startDate: "Apr 15, 2025",
      targetDate: "Jul 01, 2025",
    });

    await ctx.db.insert("projects", {
      title: "AskARA Facility Agent",
      slug: "askara",
      description:
        "LiveKit-powered voice assistant for ARA Group's FM teams with on-site context awareness.",
      tags: ["LiveKit", "11Labs", "OpenAI Realtime"],
      imageUrl: "https://cdn.alias.ai/projects/askara.png",
      heroImage: "https://cdn.alias.ai/projects/askara.png",
      featured: false,
      category: "voice",
      accentColor: "#FF914D",
      status: "qa",
      metrics: [
        { label: "Avg response time", value: "120 ms" },
        { label: "Languages", value: "6" },
      ],
      startDate: "Mar 21, 2025",
      targetDate: "Jun 30, 2025",
    });

    await ctx.db.insert("projects", {
      title: "SANCTUM Club App",
      slug: "sanctum-app",
      description:
        "Premium member experience—ticketing, F&B, Nexudus sync, and digital concierge.",
      tags: ["React Native", "Nexudus", "Stripe"],
      imageUrl: "https://cdn.alias.ai/projects/sanctum.png",
      heroImage: "https://cdn.alias.ai/projects/sanctum.png",
      featured: false,
      category: "mobile",
      accentColor: "#9B59FF",
      status: "in_progress",
      metrics: [
        { label: "Pre-registrations", value: "512" },
        { label: "VIP tiers", value: "3" },
      ],
      startDate: "May 02, 2025",
      targetDate: "Aug 15, 2025",
    });

    await ctx.db.insert("projects", {
      title: "The Estimator (Vision Pro)",
      slug: "estimator",
      description:
        "visionOS app that scans spaces and auto-quotes commercial cleaning jobs.",
      tags: ["visionOS", "RoomPlan", "ShaderGraph"],
      imageUrl: "https://cdn.alias.ai/projects/estimator.png",
      heroImage: "https://cdn.alias.ai/projects/estimator.png",
      featured: false,
      category: "xr",
      accentColor: "#00C896",
      status: "prototype",
      metrics: [
        { label: "Scan accuracy", value: "±3 cm" },
        { label: "Quotes generated", value: "43" },
      ],
      startDate: "Apr 10, 2025",
      targetDate: "Oct 01, 2025",
    });

    await ctx.db.insert("projects", {
      title: "The ALIAS Toolbox",
      slug: "toolbox",
      description:
        "Self-serve AI playground & lead-farming SaaS product for prospects.",
      tags: ["Multi-tenant", "Pay-wall", "SaaS"],
      imageUrl: "https://cdn.alias.ai/projects/toolbox.png",
      heroImage: "https://cdn.alias.ai/projects/toolbox.png",
      featured: false,
      category: "saas",
      accentColor: "#FFD500",
      status: "backlog",
      metrics: [{ label: "Planned tools", value: "15" }],
      startDate: "Jun 01, 2025",
      targetDate: "Dec 15, 2025",
    });

    /* ──────────────────────────────────────────────
       3.  Insert stats
    ────────────────────────────────────────────── */
    await ctx.db.insert("stats", {
      label: "Clients",
      value: "40+",
      color: "#0099FF",
      updated: "2025-01-01",
    });

    await ctx.db.insert("stats", {
      label: "AI Agents Orchestrated",
      value: "120+",
      color: "#FF914D",
      updated: "2025-01-01",
    });

    await ctx.db.insert("stats", {
      label: "In Operation",
      value: "3 yr",
      color: "#9B59FF",
      updated: "2025-01-01",
    });

    return "Database seeded with ALIAS project data!";
  },
});