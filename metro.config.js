// metro.config.js
const { getDefaultConfig } = require("expo/metro-config");
const config = getDefaultConfig(__dirname);

// Add Jazz-specific configuration
config.resolver.sourceExts = ["mjs", "js", "json", "ts", "tsx", "cjs"];
config.resolver.requireCycleIgnorePatterns = [/(^|\/|\\)node_modules($|\/|\\)/];

// Add resolver for nanoid issue
config.resolver.resolverMainFields = ["react-native", "browser", "main"];
config.resolver.platforms = ["ios", "android", "native", "web"];

// Keep existing server configuration
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Set custom timeout (in milliseconds)
      req.setTimeout(30000); // 30 seconds
      res.setTimeout(30000); // 30 seconds

      return middleware(req, res, next);
    };
  }
};

// Keep existing watcher configuration
config.watcher = {
  ...config.watcher,
  unstable_lazySha1: true, // Enable lazy SHA1 computation for better performance
};

module.exports = config;