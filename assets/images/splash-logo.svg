<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="100" cy="100" r="100" fill="#000000"/>
  
  <!-- ALIAS text -->
  <text x="100" y="90" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#FFFFFF" text-anchor="middle" letter-spacing="2">ALIAS</text>
  
  <!-- Blue dot -->
  <circle cx="140" cy="85" r="6" fill="#0099FF">
    <animate attributeName="r" values="6;8;6" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Subtitle -->
  <text x="100" y="120" font-family="Arial, sans-serif" font-size="10" font-weight="500" fill="#9CA3AF" text-anchor="middle" letter-spacing="1">COMPONENT GALLERY</text>
  
  <!-- Sparkle decorations -->
  <g fill="#0099FF" opacity="0.7">
    <circle cx="60" cy="60" r="2">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="140" cy="140" r="2">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="160" cy="60" r="1.5">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2.2s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
