# /add-blur-menu

Adds a beautifully animated blur menu component to your React Native/Expo project.

## Features
- ✨ Smooth spring animations with staggered menu item entrances
- 🎯 9 different FAB positions (top, bottom, center, left, right combinations)
- 🌊 Elegant blur effect using Expo's BlurView
- 🎭 Global modal manager ensures only one menu is active at a time
- 🎨 Customizable icons, titles, and styling
- 📱 Safe area aware with automatic device adaptation
- 🎪 Support for custom trigger components

## What it adds
1. **BlurMenu Component** (`/components/BlurMenu.tsx`)
   - Main component with all animation logic
   - TypeScript interfaces for proper typing
   - Cross-platform blur support (iOS native, Android fallback)

## Dependencies Required
```bash
npm install expo-blur react-native-reanimated react-native-safe-area-context @expo/vector-icons
```

## Basic Usage
```tsx
import BlurMenu, { MenuItem } from '@/components/BlurMenu';
import { useState } from 'react';

const menuItems: MenuItem[] = [
  {
    id: 'profile',
    icon: 'person-outline',
    title: 'Profile',
    onPress: () => console.log('Profile pressed')
  },
  {
    id: 'settings',
    icon: 'settings-outline', 
    title: 'Settings',
    onPress: () => console.log('Settings pressed')
  },
  {
    id: 'logout',
    icon: 'log-out-outline',
    title: 'Logout',
    onPress: () => console.log('Logout pressed')
  }
];

export default function MyScreen() {
  const [menuVisible, setMenuVisible] = useState(false);

  return (
    <BlurMenu
      visible={menuVisible}
      onToggle={() => setMenuVisible(!menuVisible)}
      menuItems={menuItems}
      title="Main Menu"
      fabPosition="bottom-right"
    />
  );
}
```

## Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `visible` | `boolean` | - | Required. Controls menu visibility |
| `onToggle` | `() => void` | - | Required. Callback when menu should toggle |
| `menuItems` | `MenuItem[]` | - | Required. Array of menu items |
| `position` | `MenuPosition` | `'center'` | Menu content alignment |
| `title` | `string` | `'Menu'` | Menu title displayed at the top |
| `fabPosition` | `FabPosition` | `'bottom-center'` | FAB trigger position |
| `children` | `React.ReactNode` | - | Custom trigger component |
| `className` | `string` | - | Additional CSS class |

## Advanced Examples

### Custom Trigger
```tsx
<BlurMenu
  visible={menuVisible}
  onToggle={() => setMenuVisible(!menuVisible)}
  menuItems={menuItems}
  title="Actions"
>
  <View style={styles.customTrigger}>
    <Ionicons name="ellipsis-horizontal" size={24} color="white" />
  </View>
</BlurMenu>
```

### Different Positions
```tsx
// Top-right corner
<BlurMenu
  fabPosition="top-right"
  position="right"
  {...otherProps}
/>

// Left-side menu
<BlurMenu
  fabPosition="left"
  position="left"
  {...otherProps}
/>
```

### Custom Icons
```tsx
const menuItems: MenuItem[] = [
  {
    // React element
    icon: <MyCustomIcon size={20} color="white" />,
    title: 'Custom Action',
    onPress: handleAction
  },
  {
    // Icon component
    icon: () => <AnotherIcon />,
    title: 'Function Icon',
    onPress: handleAnother
  },
  {
    // Ionicons name string
    icon: 'heart-outline',
    title: 'Like',
    onPress: handleLike
  }
];
```

## Position Types
- **MenuPosition**: `'left' | 'right' | 'center'`
- **FabPosition**: 
  - Top: `'top-left' | 'top-right' | 'top-center'`
  - Bottom: `'bottom-left' | 'bottom-right' | 'bottom-center'`
  - Side: `'left' | 'right' | 'center'`

## Animation Details
- Spring animations with optimized damping/stiffness
- Staggered menu item entrance (50ms delay between items)
- Direction-aware animations based on FAB position
- Smooth blur and opacity transitions (200-300ms)

## Notes
- Global modal manager prevents multiple menus from opening simultaneously
- Automatically handles safe areas on devices with notches
- Platform-specific blur implementation (native on iOS, fallback on Android)
- All animations run at 60fps using react-native-reanimated worklets