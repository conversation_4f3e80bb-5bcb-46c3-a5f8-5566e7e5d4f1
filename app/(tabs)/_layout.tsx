import { Tabs } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { TouchableOpacity, View, StyleSheet, Animated } from "react-native";
import { useRouter, usePathname } from "expo-router";
import { useFocusEffect } from "@react-navigation/native";
import { useCallback, useRef } from "react";
import * as Haptics from "expo-haptics";
import { Platform } from "react-native";
import { BlurView } from "expo-blur";

export default function TabLayout() {
  const router = useRouter();
  const pathname = usePathname();
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handleMicPress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    // Scale animation for mic button
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    router.push("/assistant");
  };

  // Navigation items configuration
  const navItems = [
    // Left side (3 items)
    { route: "/(tabs)", icon: "home", iconFilled: "home", label: "Home", color: "#0099FF" },
    { route: "/(tabs)/projects", icon: "briefcase-outline", iconFilled: "briefcase", label: "Projects", color: "#00C896" },
    { route: "/(tabs)/learn", icon: "school-outline", iconFilled: "school", label: "Learn", color: "#9B59FF" },
    // Middle button is handled separately
    // Right side (3 items)
    { route: "/(tabs)/ops", icon: "settings-outline", iconFilled: "settings", label: "Ops", color: "#FF914D" },
    { route: "/(tabs)/toolbox", icon: "construct-outline", iconFilled: "construct", label: "Toolbox", color: "#FFD500" },
    { route: "/(tabs)/profile", icon: "person-outline", iconFilled: "person", label: "Profile", color: "#FF6B9D" },
  ];

  const handleNavPress = (route: string, color: string) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.push(route as any);
  };

  const isActive = (route: string) => {
    if (route === "/(tabs)" && pathname === "/") return true;
    return pathname.includes(route.replace("/(tabs)", ""));
  };

  return (
    <>
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarStyle: { display: "none" }, // Hide default tab bar
        }}
      >
        <Tabs.Screen name="index" />
        <Tabs.Screen name="projects" />
        <Tabs.Screen name="learn" />
        <Tabs.Screen name="ops" />
        <Tabs.Screen name="toolbox" />
        <Tabs.Screen name="profile" />
      </Tabs>

      {/* Custom Liquid Glass Navigation Bar */}
      <View style={styles.customNavContainer}>
        <BlurView intensity={80} tint="dark" style={styles.liquidGlassNav}>
          {/* Left side icons (3) */}
          <View style={styles.navSide}>
            {navItems.slice(0, 3).map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.navItem,
                  isActive(item.route) && { backgroundColor: `${item.color}20` }
                ]}
                onPress={() => handleNavPress(item.route, item.color)}
              >
                <Ionicons
                  name={isActive(item.route) ? item.iconFilled as any : item.icon as any}
                  size={20}
                  color={isActive(item.route) ? item.color : "#9CA3AF"}
                />
              </TouchableOpacity>
            ))}
          </View>

          {/* Center mic button */}
          <Animated.View style={[styles.micButtonContainer, { transform: [{ scale: scaleAnim }] }]}>
            <TouchableOpacity style={styles.micButton} onPress={handleMicPress}>
              <BlurView intensity={60} tint="light" style={styles.micButtonBlur}>
                <Ionicons name="mic" size={24} color="#FFFFFF" />
              </BlurView>
            </TouchableOpacity>
          </Animated.View>

          {/* Right side icons (3) */}
          <View style={styles.navSide}>
            {navItems.slice(3, 6).map((item, index) => (
              <TouchableOpacity
                key={index + 3}
                style={[
                  styles.navItem,
                  isActive(item.route) && { backgroundColor: `${item.color}20` }
                ]}
                onPress={() => handleNavPress(item.route, item.color)}
              >
                <Ionicons
                  name={isActive(item.route) ? item.iconFilled as any : item.icon as any}
                  size={20}
                  color={isActive(item.route) ? item.color : "#9CA3AF"}
                />
              </TouchableOpacity>
            ))}
          </View>
        </BlurView>
      </View>

    </>
  );
}

const styles = StyleSheet.create({
  customNavContainer: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    height: 60,
    zIndex: 1000,
  },
  liquidGlassNav: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    borderRadius: 30,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.15)",
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.3,
        shadowRadius: 15,
      },
      android: {
        elevation: 12,
      },
    }),
  },
  navSide: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  navItem: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
  },
  micButtonContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  micButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#0099FF",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.4,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  micButtonBlur: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 153, 255, 0.8)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
});