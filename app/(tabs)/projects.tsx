import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
// Temporarily disable Jazz imports for React Native compatibility
// import CollaborationOverlay from "@/components/CollaborationOverlay";
// import JazzAuth from "@/components/JazzAuth";
import ComponentGallery from "@/components/ComponentGallery";
// import { useJazzAuth } from "@/lib/jazz/provider";

const STATUS_LABELS = {
  backlog: "Backlog",
  in_progress: "In Progress", 
  review: "Review",
  qa: "QA",
  done: "Done",
  prototype: "Prototype",
};

const STATUS_COLORS = {
  backlog: "#6B7280",
  in_progress: "#0099FF",
  review: "#FF914D",
  qa: "#9B59FF",
  done: "#00C896",
  prototype: "#FFD500",
};

export default function Projects() {
  const projects = useQuery(api.projects.list);
  const currentUser = useQuery(api.auth.getCurrentUser);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [showJazzAuth, setShowJazzAuth] = useState(false);
  const [activeTab, setActiveTab] = useState<'projects' | 'components'>('projects');
  // Temporarily disable Jazz while fixing React Native compatibility
  const isJazzAuthenticated = false;

  const handlePress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleProjectPress = (slug: string) => {
    handlePress();
    router.push(`/project/${slug}`);
  };

  const handleStatusFilter = (status: string) => {
    handlePress();
    setSelectedStatus(selectedStatus === status ? null : status);
  };

  if (projects === undefined || !currentUser) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const filteredProjects = selectedStatus 
    ? projects.filter(p => p.status === selectedStatus)
    : projects;

  const canEdit = ["admin", "lead", "consultant"].includes(currentUser.role);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <View>
              <Text style={styles.title}>
                {activeTab === 'projects' ? 'Projects' : 'Component Gallery'}
              </Text>
              <Text style={styles.subtitle}>
                {activeTab === 'projects'
                  ? `${filteredProjects.length} project${filteredProjects.length !== 1 ? 's' : ''}`
                  : 'Collaborative component library'
                }
              </Text>
            </View>

            {/* Jazz Collaboration Button */}
            <TouchableOpacity
              style={[
                styles.collaborationButton,
                isJazzAuthenticated && styles.collaborationButtonActive
              ]}
              onPress={() => setShowJazzAuth(true)}
              activeOpacity={0.8}
            >
              <Ionicons
                name={isJazzAuthenticated ? "people" : "flash"}
                size={16}
                color={isJazzAuthenticated ? "#00C896" : "#0099FF"}
              />
              <Text style={[
                styles.collaborationButtonText,
                isJazzAuthenticated && styles.collaborationButtonTextActive
              ]}>
                {isJazzAuthenticated ? "Live" : "Enable Collaboration"}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'projects' && styles.activeTab
              ]}
              onPress={() => setActiveTab('projects')}
              activeOpacity={0.8}
            >
              <Ionicons
                name="briefcase"
                size={16}
                color={activeTab === 'projects' ? "#0099FF" : "#6B7280"}
              />
              <Text style={[
                styles.tabText,
                activeTab === 'projects' && styles.activeTabText
              ]}>
                Projects
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'components' && styles.activeTab
              ]}
              onPress={() => setActiveTab('components')}
              activeOpacity={0.8}
            >
              <Ionicons
                name="code-slash"
                size={16}
                color={activeTab === 'components' ? "#0099FF" : "#6B7280"}
              />
              <Text style={[
                styles.tabText,
                activeTab === 'components' && styles.activeTabText
              ]}>
                Components
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Content based on active tab */}
        {activeTab === 'projects' ? (
          <>
            {/* Status Filter Chips */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.filtersContainer}
            >
              {Object.entries(STATUS_LABELS).map(([key, label]) => (
                <TouchableOpacity
                  key={key}
                  style={[
                    styles.filterChip,
                    selectedStatus === key && styles.filterChipActive,
                    { borderColor: STATUS_COLORS[key as keyof typeof STATUS_COLORS] }
                  ]}
                  onPress={() => handleStatusFilter(key)}
                  activeOpacity={0.8}
                >
                  <Text style={[
                    styles.filterChipText,
                    selectedStatus === key && styles.filterChipTextActive,
                    { color: selectedStatus === key ? "white" : STATUS_COLORS[key as keyof typeof STATUS_COLORS] }
                  ]}>
                    {label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

        {/* Projects Grid */}
        <View style={styles.projectsGrid}>
          {filteredProjects.map((project) => (
            <TouchableOpacity 
              key={project._id}
              style={styles.projectCard}
              onPress={() => handleProjectPress(project.slug)}
              activeOpacity={0.95}
            >
              <ImageBackground
                source={{ uri: project.imageUrl }}
                style={styles.projectBackground}
                imageStyle={styles.projectBackgroundImage}
              >
                <View style={styles.projectOverlay}>
                  <View style={styles.projectHeader}>
                    <View style={[
                      styles.statusBadge, 
                      { backgroundColor: STATUS_COLORS[project.status] + "20" }
                    ]}>
                      <Text style={[
                        styles.statusText, 
                        { color: STATUS_COLORS[project.status] }
                      ]}>
                        {STATUS_LABELS[project.status]}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.projectContent}>
                    <Text style={styles.projectTitle}>{project.title}</Text>
                    <Text style={styles.projectDescription} numberOfLines={2}>
                      {project.description}
                    </Text>
                    
                    <View style={styles.projectTags}>
                      {project.tags.slice(0, 2).map((tag, index) => (
                        <View 
                          key={index} 
                          style={[
                            styles.projectTag,
                            { 
                              backgroundColor: project.accentColor + "15", 
                              borderColor: project.accentColor + "30" 
                            }
                          ]}
                        >
                          <Text style={[
                            styles.projectTagText, 
                            { color: project.accentColor }
                          ]}>
                            {tag}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </View>
              </ImageBackground>
            </TouchableOpacity>
          ))}
        </View>

            {/* Empty State */}
            {filteredProjects.length === 0 && (
              <View style={styles.emptyState}>
                <Ionicons name="briefcase-outline" size={48} color="#6B7280" />
                <Text style={styles.emptyTitle}>No projects found</Text>
                <Text style={styles.emptyDescription}>
                  {selectedStatus
                    ? `No projects with status "${STATUS_LABELS[selectedStatus as keyof typeof STATUS_LABELS]}"`
                    : "No projects available"
                  }
                </Text>
              </View>
            )}
          </>
        ) : (
          /* Component Gallery Tab */
          <View style={styles.componentGalleryContainer}>
            <ComponentGallery
              editable={canEdit}
              showCollaboration={true}
            />
          </View>
        )}
      </ScrollView>

      {/* Temporarily disabled Jazz components for React Native compatibility */}
      {/*
      <CollaborationOverlay
        componentId="projects-gallery"
        enabled={isJazzAuthenticated}
      />

      {showJazzAuth && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={styles.modalClose}
              onPress={() => setShowJazzAuth(false)}
            >
              <Ionicons name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>

            <JazzAuth onAuthComplete={() => setShowJazzAuth(false)} />
          </View>
        </View>
      )}
      */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 80, // Account for mic button
    paddingBottom: 100, // Account for smaller floating nav bar
    gap: 24,
  },
  header: {
    gap: 4,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    gap: 16,
  },
  collaborationButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    backgroundColor: "rgba(0, 153, 255, 0.1)",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 153, 255, 0.3)",
  },
  collaborationButtonActive: {
    backgroundColor: "rgba(0, 200, 150, 0.1)",
    borderColor: "rgba(0, 200, 150, 0.3)",
  },
  collaborationButtonText: {
    color: "#0099FF",
    fontSize: 12,
    fontWeight: "500",
  },
  collaborationButtonTextActive: {
    color: "#00C896",
  },
  title: {
    color: "#FFFFFF",
    fontSize: 32,
    fontWeight: "300",
    letterSpacing: -1,
  },
  subtitle: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  filtersContainer: {
    flexDirection: "row",
    gap: 8,
    paddingHorizontal: 4,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    backgroundColor: "transparent",
  },
  filterChipActive: {
    backgroundColor: "#0099FF",
    borderColor: "#0099FF",
  },
  filterChipText: {
    fontSize: 12,
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  filterChipTextActive: {
    color: "white",
  },
  projectsGrid: {
    gap: 16,
  },
  projectCard: {
    height: 280,
    borderRadius: 20,
    overflow: "hidden",
  },
  projectBackground: {
    flex: 1,
  },
  projectBackgroundImage: {
    borderRadius: 20,
  },
  projectOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    padding: 20,
    justifyContent: "space-between",
  },
  projectHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  projectContent: {
    gap: 8,
  },
  projectTitle: {
    color: "white",
    fontSize: 24,
    fontWeight: "300",
    letterSpacing: -0.5,
  },
  projectDescription: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 13,
    lineHeight: 18,
  },
  projectTags: {
    flexDirection: "row",
    gap: 6,
    marginTop: 4,
  },
  projectTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  projectTagText: {
    fontSize: 9,
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
    gap: 16,
  },
  emptyTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  emptyDescription: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    maxWidth: 280,
  },
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: "#1A1A1A",
    borderRadius: 20,
    padding: 24,
    margin: 20,
    maxWidth: 400,
    width: "100%",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
  modalClose: {
    position: "absolute",
    top: 16,
    right: 16,
    zIndex: 1001,
    padding: 4,
  },
  tabContainer: {
    flexDirection: "row",
    marginTop: 16,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 6,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: "rgba(0, 153, 255, 0.1)",
  },
  tabText: {
    color: "#6B7280",
    fontSize: 14,
    fontWeight: "500",
  },
  activeTabText: {
    color: "#0099FF",
  },
  componentGalleryContainer: {
    flex: 1,
    minHeight: 400,
  },
});