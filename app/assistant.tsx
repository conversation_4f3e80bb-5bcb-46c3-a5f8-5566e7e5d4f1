import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import * as Haptics from "expo-haptics";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

const SUGGESTED_ACTIONS = [
  "Show me project status",
  "What's my schedule today?",
  "Create a new task",
  "Check team availability",
  "Generate project report",
  "Book a meeting room",
];

export default function Assistant() {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Animation values
  const waveformAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;

  useEffect(() => {
    // Slide up animation when component mounts
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    return () => {
      // Cleanup animations
      waveformAnim.stopAnimation();
      pulseAnim.stopAnimation();
    };
  }, []);

  useEffect(() => {
    if (isListening) {
      // Start waveform animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(waveformAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(waveformAnim, {
            toValue: 0,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      waveformAnim.stopAnimation();
      pulseAnim.stopAnimation();
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [isListening]);

  const handleClose = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    Animated.timing(slideAnim, {
      toValue: SCREEN_HEIGHT,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      router.back();
    });
  };

  const handleMicPress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    if (isListening) {
      // Stop listening
      setIsListening(false);
      setIsProcessing(true);
      
      // Simulate processing
      setTimeout(() => {
        setIsProcessing(false);
        setTranscript("I understand you want to check project status. Here's what I found...");
      }, 2000);
    } else {
      // Start listening
      setIsListening(true);
      setTranscript("");
      setIsProcessing(false);
    }
  };

  const handleSuggestedAction = (action: string) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    setTranscript(`Processing: "${action}"`);
    setIsProcessing(true);
    
    setTimeout(() => {
      setIsProcessing(false);
      setTranscript(`Here's the information for: "${action}"`);
    }, 1500);
  };

  const renderWaveform = () => {
    const bars = Array.from({ length: 5 }, (_, i) => {
      const animatedHeight = waveformAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [4, 20 + Math.random() * 20],
      });

      return (
        <Animated.View
          key={i}
          style={[
            styles.waveformBar,
            {
              height: animatedHeight,
              marginHorizontal: 2,
            },
          ]}
        />
      );
    });

    return <View style={styles.waveform}>{bars}</View>;
  };

  return (
    <View style={styles.overlay}>
      <TouchableOpacity 
        style={styles.backdrop} 
        onPress={handleClose}
        activeOpacity={1}
      />
      
      <Animated.View 
        style={[
          styles.container,
          {
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <SafeAreaView style={styles.safeArea}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <Text style={styles.title}>ALIAS Assistant</Text>
              <Text style={styles.subtitle}>
                {isListening ? "Listening..." : isProcessing ? "Processing..." : "Tap to speak"}
              </Text>
            </View>
            <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
              <Ionicons name="close" size={24} color="#9CA3AF" />
            </TouchableOpacity>
          </View>

          {/* Voice Interface */}
          <View style={styles.voiceInterface}>
            {/* Waveform */}
            {isListening && (
              <View style={styles.waveformContainer}>
                {renderWaveform()}
              </View>
            )}

            {/* Mic Button */}
            <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
              <TouchableOpacity
                style={[
                  styles.micButton,
                  isListening && styles.micButtonActive,
                  isProcessing && styles.micButtonProcessing,
                ]}
                onPress={handleMicPress}
                activeOpacity={0.8}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <Animated.View
                    style={[
                      styles.processingSpinner,
                      {
                        transform: [
                          {
                            rotate: waveformAnim.interpolate({
                              inputRange: [0, 1],
                              outputRange: ["0deg", "360deg"],
                            }),
                          },
                        ],
                      },
                    ]}
                  >
                    <Ionicons name="sync" size={32} color="white" />
                  </Animated.View>
                ) : (
                  <Ionicons 
                    name={isListening ? "mic" : "mic-outline"} 
                    size={32} 
                    color="white" 
                  />
                )}
              </TouchableOpacity>
            </Animated.View>

            {/* Status Text */}
            <Text style={styles.statusText}>
              {isListening 
                ? "Speak now..." 
                : isProcessing 
                ? "Thinking..." 
                : "Tap the microphone to start"
              }
            </Text>
          </View>

          {/* Transcript */}
          {transcript && (
            <View style={styles.transcriptContainer}>
              <Text style={styles.transcriptText}>{transcript}</Text>
            </View>
          )}

          {/* Suggested Actions */}
          {!isListening && !isProcessing && (
            <View style={styles.suggestedActions}>
              <Text style={styles.suggestedTitle}>Try saying:</Text>
              <View style={styles.actionsList}>
                {SUGGESTED_ACTIONS.map((action, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.actionChip}
                    onPress={() => handleSuggestedAction(action)}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.actionText}>{action}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
        </SafeAreaView>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  backdrop: {
    flex: 1,
  },
  container: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#000000",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: SCREEN_HEIGHT * 0.85,
  },
  safeArea: {
    flex: 1,
    padding: 24,
    gap: 24,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerContent: {
    gap: 4,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  subtitle: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#18181B",
    justifyContent: "center",
    alignItems: "center",
  },
  voiceInterface: {
    alignItems: "center",
    gap: 24,
    paddingVertical: 32,
  },
  waveformContainer: {
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  waveform: {
    flexDirection: "row",
    alignItems: "center",
    height: 40,
  },
  waveformBar: {
    width: 4,
    backgroundColor: "#0099FF",
    borderRadius: 2,
  },
  micButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#0099FF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#0099FF",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  micButtonActive: {
    backgroundColor: "#00C896",
    shadowColor: "#00C896",
  },
  micButtonProcessing: {
    backgroundColor: "#FF914D",
    shadowColor: "#FF914D",
  },
  processingSpinner: {
    justifyContent: "center",
    alignItems: "center",
  },
  statusText: {
    color: "#D1D5DB",
    fontSize: 16,
    textAlign: "center",
  },
  transcriptContainer: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    borderLeftColor: "#0099FF",
  },
  transcriptText: {
    color: "#FFFFFF",
    fontSize: 16,
    lineHeight: 24,
  },
  suggestedActions: {
    gap: 16,
  },
  suggestedTitle: {
    color: "#9CA3AF",
    fontSize: 14,
    fontWeight: "500",
  },
  actionsList: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  actionChip: {
    backgroundColor: "#18181B",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#374151",
  },
  actionText: {
    color: "#D1D5DB",
    fontSize: 13,
  },
});