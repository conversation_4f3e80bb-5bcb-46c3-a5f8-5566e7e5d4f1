import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Image,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from "react-native-reanimated";

const FEATURED_PODCASTS = [
  {
    id: 1,
    title: "The AI Revolution",
    host: "<PERSON>",
    followers: "2.1M",
    rating: 4.8,
    image: "https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=400&q=80",
    color: "#EF4444",
    icon: "heart",
  },
  {
    id: 2,
    title: "Future of Work",
    host: "<PERSON>",
    followers: "890K",
    rating: 4.9,
    image: "https://images.unsplash.com/photo-1622737133809-d95047b9e673?w=400&q=80",
    color: "#8B5CF6",
    icon: "bookmark",
  },
  {
    id: 3,
    title: "Digital Transformation",
    host: "<PERSON> Foster",
    followers: "1.5M",
    rating: 4.7,
    image: "https://images.unsplash.com/photo-1633596683562-4a47eb4983c5?w=400&q=80",
    color: "#10B981",
    icon: "headset",
  },
];

const NOW_PLAYING = {
  title: "ALIAS Intelligence Briefing",
  category: "AI & Technology",
  host: "Dan Humphreys",
  image: "https://images.unsplash.com/photo-1643228995868-bf698f67d053?w=400&q=80",
  currentTime: "12:35",
  totalTime: "36:42",
  progress: 0.35,
};

const RECENT_EPISODES = [
  { title: "Building AI Agents", duration: "24 min", isPlaying: true },
  { title: "Future of Automation", duration: "31 min", isPlaying: false },
];

export default function PodcastDiscovery() {
  const [likedPodcasts, setLikedPodcasts] = useState<number[]>([]);
  const [addedPodcasts, setAddedPodcasts] = useState<number[]>([]);
  const [isPlaying, setIsPlaying] = useState(true);

  const scaleAnim = useSharedValue(1);

  const handlePress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleLike = (podcastId: number) => {
    handlePress();
    scaleAnim.value = withSpring(1.1, { damping: 15 }, () => {
      scaleAnim.value = withSpring(1);
    });
    
    setLikedPodcasts(prev => 
      prev.includes(podcastId) 
        ? prev.filter(id => id !== podcastId)
        : [...prev, podcastId]
    );
  };

  const handleAdd = (podcastId: number) => {
    handlePress();
    setAddedPodcasts(prev => 
      prev.includes(podcastId) 
        ? prev.filter(id => id !== podcastId)
        : [...prev, podcastId]
    );
  };

  const handlePlayPause = () => {
    handlePress();
    setIsPlaying(!isPlaying);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim.value }],
  }));

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>ALIAS Audio</Text>
        </View>

        <View style={styles.mainContent}>
          {/* Left Column */}
          <View style={styles.leftColumn}>
            {/* Featured Section */}
            <View style={styles.featuredSection}>
              <View style={styles.badge}>
                <Ionicons name="trending-up" size={16} color="#8B5CF6" />
                <Text style={styles.badgeText}>Weekly Trending</Text>
              </View>
              
              <Text style={styles.featuredTitle}>
                Featured podcasts{'\n'}this week
              </Text>
              <Text style={styles.featuredSubtitle}>
                Handpicked by our editorial team and loved by millions of listeners worldwide
              </Text>

              {/* Podcast List */}
              <View style={styles.podcastList}>
                {FEATURED_PODCASTS.map((podcast) => (
                  <View key={podcast.id} style={styles.podcastItem}>
                    <View style={styles.podcastLeft}>
                      <Image source={{ uri: podcast.image }} style={styles.podcastImage} />
                      <Animated.View style={animatedStyle}>
                        <TouchableOpacity
                          style={[styles.likeButton, { backgroundColor: podcast.color }]}
                          onPress={() => handleLike(podcast.id)}
                        >
                          <Ionicons 
                            name={podcast.icon as any} 
                            size={16} 
                            color="white" 
                          />
                        </TouchableOpacity>
                      </Animated.View>
                      <View style={styles.podcastInfo}>
                        <Text style={styles.podcastTitle}>{podcast.title}</Text>
                        <Text style={styles.podcastHost}>{podcast.host} • {podcast.followers} followers</Text>
                      </View>
                      <View style={styles.rating}>
                        <Ionicons name="star" size={16} color="#F59E0B" />
                        <Text style={styles.ratingText}>{podcast.rating}</Text>
                      </View>
                    </View>
                    <TouchableOpacity
                      style={[
                        styles.addButton,
                        addedPodcasts.includes(podcast.id) && styles.addButtonActive
                      ]}
                      onPress={() => handleAdd(podcast.id)}
                    >
                      <Text style={[
                        styles.addButtonText,
                        addedPodcasts.includes(podcast.id) && styles.addButtonTextActive
                      ]}>
                        {addedPodcasts.includes(podcast.id) ? "Added" : "Add"}
                      </Text>
                      <Ionicons 
                        name={addedPodcasts.includes(podcast.id) ? "checkmark" : "add"} 
                        size={16} 
                        color={addedPodcasts.includes(podcast.id) ? "white" : "#374151"} 
                      />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>

            {/* Stats Cards */}
            <View style={styles.statsGrid}>
              {/* Analytics Card */}
              <View style={styles.analyticsCard}>
                <View style={styles.cardHeader}>
                  <View style={styles.analyticsTag}>
                    <Ionicons name="analytics" size={12} color="#60A5FA" />
                    <Text style={styles.analyticsTagText}>Analytics</Text>
                  </View>
                  <TouchableOpacity>
                    <Ionicons name="ellipsis-horizontal" size={16} color="#9CA3AF" />
                  </TouchableOpacity>
                </View>
                <View style={styles.cardContent}>
                  <Text style={styles.cardTitle}>Weekly Stats</Text>
                  <Text style={styles.cardSubtitle}>12.5M total plays</Text>
                </View>
                <TouchableOpacity style={styles.viewDetailsButton}>
                  <Text style={styles.viewDetailsText}>View Details</Text>
                  <Ionicons name="arrow-forward" size={16} color="#60A5FA" />
                </TouchableOpacity>
                {/* Decorative bars */}
                <View style={styles.decorativeBars}>
                  <View style={[styles.bar, { height: 48, backgroundColor: "#60A5FA" }]} />
                  <View style={[styles.bar, { height: 64, backgroundColor: "#A855F7" }]} />
                  <View style={[styles.bar, { height: 32, backgroundColor: "#EC4899" }]} />
                </View>
              </View>

              {/* Featured Creator Card */}
              <ImageBackground
                source={{ uri: "https://images.unsplash.com/photo-1629946832022-c327f74956e0?w=600&q=80" }}
                style={styles.creatorCard}
                imageStyle={styles.creatorCardImage}
              >
                <View style={styles.creatorOverlay}>
                  <View style={styles.creatorInfo}>
                    <View style={styles.creatorHeader}>
                      <Text style={styles.creatorName}>Dan Humphreys</Text>
                      <Ionicons name="checkmark-circle" size={20} color="white" />
                    </View>
                    <Text style={styles.creatorRole}>AI & Technology Expert</Text>
                    <View style={styles.creatorStats}>
                      <View style={styles.statItem}>
                        <Ionicons name="people" size={16} color="white" />
                        <Text style={styles.statText}>2.8M followers</Text>
                      </View>
                      <View style={styles.statItem}>
                        <Ionicons name="mic" size={16} color="white" />
                        <Text style={styles.statText}>127 episodes</Text>
                      </View>
                    </View>
                  </View>
                  <TouchableOpacity style={styles.followButton}>
                    <Ionicons name="person-add" size={16} color="white" />
                    <Text style={styles.followButtonText}>Follow</Text>
                  </TouchableOpacity>
                </View>
              </ImageBackground>
            </View>
          </View>

          {/* Right Column */}
          <View style={styles.rightColumn}>
            {/* Now Playing Card */}
            <View style={styles.nowPlayingCard}>
              <View style={styles.nowPlayingHeader}>
                <View style={styles.nowPlayingTag}>
                  <Ionicons name="play-circle" size={16} color="#10B981" />
                  <Text style={styles.nowPlayingTagText}>Now Playing</Text>
                </View>
                <TouchableOpacity>
                  <Ionicons name="ellipsis-horizontal" size={20} color="#9CA3AF" />
                </TouchableOpacity>
              </View>

              <Image source={{ uri: NOW_PLAYING.image }} style={styles.nowPlayingImage} />
              
              <View style={styles.nowPlayingInfo}>
                <Text style={styles.nowPlayingTitle}>{NOW_PLAYING.title}</Text>
                <Text style={styles.nowPlayingCategory}>{NOW_PLAYING.category}</Text>
                <Text style={styles.nowPlayingHost}>Hosted by {NOW_PLAYING.host}</Text>
              </View>

              {/* Progress Bar */}
              <View style={styles.progressSection}>
                <View style={styles.progressBar}>
                  <View style={[styles.progressFill, { width: `${NOW_PLAYING.progress * 100}%` }]} />
                </View>
                <View style={styles.progressTime}>
                  <Text style={styles.timeText}>{NOW_PLAYING.currentTime}</Text>
                  <Text style={styles.timeText}>{NOW_PLAYING.totalTime}</Text>
                </View>
              </View>

              {/* Controls */}
              <View style={styles.controls}>
                <TouchableOpacity style={styles.controlButton}>
                  <Ionicons name="play-skip-back" size={20} color="#9CA3AF" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.playButton} onPress={handlePlayPause}>
                  <Ionicons 
                    name={isPlaying ? "pause" : "play"} 
                    size={20} 
                    color="white" 
                  />
                </TouchableOpacity>
                <TouchableOpacity style={styles.controlButton}>
                  <Ionicons name="play-skip-forward" size={20} color="#9CA3AF" />
                </TouchableOpacity>
              </View>

              {/* Recent Episodes */}
              <View style={styles.recentEpisodes}>
                <Text style={styles.recentTitle}>Recent Episodes</Text>
                {RECENT_EPISODES.map((episode, index) => (
                  <View key={index} style={styles.episodeItem}>
                    <View style={styles.episodeLeft}>
                      <Ionicons 
                        name={episode.isPlaying ? "play" : "time"} 
                        size={16} 
                        color={episode.isPlaying ? "#10B981" : "#9CA3AF"} 
                      />
                      <Text style={styles.episodeTitle}>{episode.title}</Text>
                    </View>
                    <Text style={styles.episodeDuration}>{episode.duration}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Discover Categories Card */}
            <View style={styles.discoverCard}>
              <Image 
                source={{ uri: "https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=400&q=80" }}
                style={styles.discoverImage}
              />
              <Text style={styles.discoverTitle}>Discover New Categories</Text>
              <Text style={styles.discoverSubtitle}>
                Explore curated playlists across different genres and topics
              </Text>
              
              <View style={styles.categoryTags}>
                {["Business", "Technology", "Health", "Education"].map((category, index) => (
                  <View key={index} style={styles.categoryTag}>
                    <Text style={styles.categoryTagText}>{category}</Text>
                  </View>
                ))}
              </View>
              
              <TouchableOpacity style={styles.exploreButton}>
                <Ionicons name="compass" size={16} color="white" />
                <Text style={styles.exploreButtonText}>Explore All</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#686873",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
    paddingTop: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  headerTitle: {
    color: "white",
    fontSize: 24,
    fontWeight: "700",
  },
  mainContent: {
    flexDirection: "row",
    gap: 24,
  },
  leftColumn: {
    flex: 2,
    gap: 24,
  },
  rightColumn: {
    flex: 1,
    gap: 24,
  },
  
  // Featured Section
  featuredSection: {
    backgroundColor: "#F3F0F9",
    borderRadius: 24,
    padding: 24,
  },
  badge: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignSelf: "flex-start",
    marginBottom: 24,
  },
  badgeText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#7C3AED",
  },
  featuredTitle: {
    fontSize: 32,
    fontWeight: "800",
    color: "#1F2937",
    marginBottom: 8,
    lineHeight: 38,
  },
  featuredSubtitle: {
    fontSize: 16,
    color: "#6B7280",
    marginBottom: 32,
    lineHeight: 24,
  },
  podcastList: {
    gap: 16,
  },
  podcastItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    borderRadius: 16,
    padding: 16,
  },
  podcastLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 12,
  },
  podcastImage: {
    width: 48,
    height: 48,
    borderRadius: 12,
  },
  likeButton: {
    width: 36,
    height: 36,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  podcastInfo: {
    flex: 1,
    minWidth: 0,
  },
  podcastTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
  },
  podcastHost: {
    fontSize: 14,
    color: "#6B7280",
    marginTop: 2,
  },
  rating: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#F59E0B",
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    backgroundColor: "white",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    marginLeft: 12,
  },
  addButtonActive: {
    backgroundColor: "#1F2937",
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#374151",
  },
  addButtonTextActive: {
    color: "white",
  },

  // Stats Grid
  statsGrid: {
    flexDirection: "row",
    gap: 16,
  },
  analyticsCard: {
    flex: 1,
    backgroundColor: "#0B0B15",
    borderRadius: 24,
    padding: 24,
    minHeight: 200,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  analyticsTag: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(96, 165, 250, 0.2)",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 20,
  },
  analyticsTagText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#60A5FA",
    textTransform: "uppercase",
  },
  cardContent: {
    flex: 1,
    justifyContent: "center",
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "white",
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 14,
    color: "#9CA3AF",
  },
  viewDetailsButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginTop: 24,
  },
  viewDetailsText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#60A5FA",
  },
  decorativeBars: {
    position: "absolute",
    bottom: 24,
    right: 24,
    flexDirection: "row",
    alignItems: "flex-end",
    gap: 8,
  },
  bar: {
    width: 16,
    borderRadius: 8,
  },

  // Creator Card
  creatorCard: {
    flex: 2,
    borderRadius: 24,
    overflow: "hidden",
    minHeight: 200,
  },
  creatorCardImage: {
    borderRadius: 24,
  },
  creatorOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    padding: 24,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  creatorInfo: {
    flex: 1,
  },
  creatorHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  creatorName: {
    fontSize: 24,
    fontWeight: "600",
    color: "white",
  },
  creatorRole: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    marginBottom: 12,
  },
  creatorStats: {
    gap: 8,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  statText: {
    fontSize: 14,
    fontWeight: "500",
    color: "white",
  },
  followButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "#1F2937",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 16,
  },
  followButtonText: {
    fontSize: 16,
    fontWeight: "500",
    color: "white",
  },

  // Now Playing Card
  nowPlayingCard: {
    backgroundColor: "#0B0B15",
    borderRadius: 24,
    padding: 24,
  },
  nowPlayingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24,
  },
  nowPlayingTag: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  nowPlayingTagText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#10B981",
    textTransform: "uppercase",
  },
  nowPlayingImage: {
    width: 112,
    height: 112,
    borderRadius: 16,
    alignSelf: "center",
    marginBottom: 24,
  },
  nowPlayingInfo: {
    alignItems: "center",
    marginBottom: 24,
  },
  nowPlayingTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "white",
    textAlign: "center",
    marginBottom: 8,
  },
  nowPlayingCategory: {
    fontSize: 14,
    color: "#9CA3AF",
    marginBottom: 4,
  },
  nowPlayingHost: {
    fontSize: 12,
    color: "#6B7280",
  },
  progressSection: {
    marginBottom: 24,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#374151",
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#10B981",
    borderRadius: 2,
  },
  progressTime: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  timeText: {
    fontSize: 12,
    color: "#9CA3AF",
  },
  controls: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 24,
    marginBottom: 24,
  },
  controlButton: {
    padding: 8,
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#10B981",
    justifyContent: "center",
    alignItems: "center",
  },
  recentEpisodes: {
    gap: 12,
  },
  recentTitle: {
    fontSize: 14,
    fontWeight: "500",
    color: "#D1D5DB",
    marginBottom: 8,
  },
  episodeItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
  },
  episodeLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    flex: 1,
  },
  episodeTitle: {
    fontSize: 14,
    color: "white",
    flex: 1,
  },
  episodeDuration: {
    fontSize: 12,
    color: "#9CA3AF",
  },

  // Discover Card
  discoverCard: {
    backgroundColor: "#F4F1FB",
    borderRadius: 24,
    padding: 32,
    alignItems: "center",
  },
  discoverImage: {
    width: 120,
    height: 120,
    borderRadius: 16,
    marginBottom: 24,
    borderWidth: 4,
    borderColor: "white",
  },
  discoverTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#1F2937",
    textAlign: "center",
    marginBottom: 12,
  },
  discoverSubtitle: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 20,
  },
  categoryTags: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    gap: 8,
    marginBottom: 24,
  },
  categoryTag: {
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 20,
  },
  categoryTagText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#374151",
  },
  exploreButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "#1F2937",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 16,
  },
  exploreButtonText: {
    fontSize: 16,
    fontWeight: "500",
    color: "white",
  },
});