import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Platform,
  Alert,
  ActionSheetIOS,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLocalSearchParams, router } from "expo-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { Id } from "@/convex/_generated/dataModel";

const STATUS_PROGRESS = {
  backlog: 0,
  in_progress: 40,
  review: 70,
  qa: 85,
  done: 100,
  prototype: 25,
};

const STATUS_LABELS = {
  backlog: "Backlog",
  in_progress: "In Progress",
  review: "Review",
  qa: "QA",
  done: "Done",
  prototype: "Prototype",
};

export default function ProjectDetail() {
  const { slug } = useLocalSearchParams<{ slug: string }>();
  const project = useQuery(api.projects.getBySlug, { slug: slug! });
  const updateStatus = useMutation(api.projects.updateStatus);

  const handlePress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleStatusChange = () => {
    if (!project) return;
    
    handlePress();
    
    const options = Object.entries(STATUS_LABELS).map(([key, label]) => label);
    const cancelButtonIndex = options.length;
    
    if (Platform.OS === "ios") {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: [...options, "Cancel"],
          cancelButtonIndex,
          title: "Change Status",
        },
        async (buttonIndex) => {
          if (buttonIndex !== cancelButtonIndex) {
            const statusKey = Object.keys(STATUS_LABELS)[buttonIndex] as keyof typeof STATUS_LABELS;
            try {
              await updateStatus({
                projectId: project._id,
                status: statusKey,
              });
            } catch (error) {
              Alert.alert("Error", "Failed to update status");
            }
          }
        }
      );
    } else {
      // For Android/Web, show a simple alert with options
      Alert.alert(
        "Change Status",
        "Select new status:",
        Object.entries(STATUS_LABELS).map(([key, label]) => ({
          text: label,
          onPress: async () => {
            try {
              await updateStatus({
                projectId: project._id,
                status: key as keyof typeof STATUS_LABELS,
              });
            } catch (error) {
              Alert.alert("Error", "Failed to update status");
            }
          },
        })).concat([{ text: "Cancel", style: "cancel" }])
      );
    }
  };

  if (project === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (project === null) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Project not found</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const progress = STATUS_PROGRESS[project.status];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Back Button */}
        <TouchableOpacity 
          style={styles.backButtonFixed}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        {/* Hero Section */}
        <View style={styles.heroContainer}>
          <ImageBackground
            source={{ uri: project.heroImage }}
            style={styles.heroBackground}
            imageStyle={styles.heroBackgroundImage}
          >
            <View style={styles.heroOverlay}>
              <View style={styles.heroContent}>
                <Text style={styles.heroTitle}>{project.title}</Text>
                <View style={styles.tagsContainer}>
                  {project.tags.map((tag, index) => (
                    <View 
                      key={index} 
                      style={[
                        styles.tag,
                        { backgroundColor: project.accentColor + "20", borderColor: project.accentColor + "40" }
                      ]}
                    >
                      <Text style={[styles.tagText, { color: project.accentColor }]}>{tag}</Text>
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </ImageBackground>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {/* Key Metrics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Key Metrics</Text>
            <View style={styles.metricsGrid}>
              {project.metrics.map((metric, index) => (
                <View key={index} style={styles.metricCard}>
                  <Text style={[styles.metricValue, { color: project.accentColor }]}>
                    {metric.value}
                  </Text>
                  <Text style={styles.metricLabel}>{metric.label}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Progress Indicator */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Progress</Text>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { 
                      width: `${progress}%`,
                      backgroundColor: project.accentColor 
                    }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>{progress}% Complete</Text>
              <View style={styles.datesContainer}>
                <Text style={styles.dateText}>Start: {project.startDate}</Text>
                <Text style={styles.dateText}>Target: {project.targetDate}</Text>
              </View>
            </View>
          </View>

          {/* Status Changer */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Status</Text>
            <View style={styles.statusContainer}>
              <View style={styles.currentStatus}>
                <Text style={styles.currentStatusLabel}>Current Status</Text>
                <Text style={[styles.currentStatusValue, { color: project.accentColor }]}>
                  {STATUS_LABELS[project.status]}
                </Text>
              </View>
              <TouchableOpacity 
                style={[styles.changeStatusButton, { backgroundColor: project.accentColor }]}
                onPress={handleStatusChange}
                activeOpacity={0.8}
              >
                <Text style={styles.changeStatusText}>Change Status</Text>
                <Ionicons name="chevron-down" size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Description */}
          {project.description && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text style={styles.description}>{project.description}</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  backButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: "#18181B",
    borderRadius: 8,
  },
  backButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  backButtonFixed: {
    position: "absolute",
    top: 60,
    left: 16,
    zIndex: 10,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 12,
    borderRadius: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  
  // Hero Section
  heroContainer: {
    height: 400,
    marginBottom: 24,
  },
  heroBackground: {
    flex: 1,
  },
  heroBackgroundImage: {
    // No border radius for full-width hero
  },
  heroOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    justifyContent: "flex-end",
  },
  heroContent: {
    padding: 24,
    gap: 16,
  },
  heroTitle: {
    color: "white",
    fontSize: 36,
    fontWeight: "300",
    letterSpacing: -1,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
  },
  tagText: {
    fontSize: 10,
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 1,
  },

  // Content
  content: {
    padding: 24,
    gap: 32,
  },
  section: {
    gap: 16,
  },
  sectionTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
    letterSpacing: -0.5,
  },

  // Metrics
  metricsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
  },
  metricCard: {
    backgroundColor: "#18181B",
    padding: 20,
    borderRadius: 16,
    flex: 1,
    minWidth: 140,
    alignItems: "center",
    gap: 8,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: "700",
  },
  metricLabel: {
    color: "#9CA3AF",
    fontSize: 12,
    textTransform: "uppercase",
    letterSpacing: 1,
    textAlign: "center",
  },

  // Progress
  progressContainer: {
    gap: 12,
  },
  progressBar: {
    height: 8,
    backgroundColor: "#374151",
    borderRadius: 4,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    borderRadius: 4,
  },
  progressText: {
    color: "#D1D5DB",
    fontSize: 14,
    fontWeight: "500",
  },
  datesContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  dateText: {
    color: "#9CA3AF",
    fontSize: 12,
  },

  // Status
  statusContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#18181B",
    padding: 20,
    borderRadius: 16,
  },
  currentStatus: {
    gap: 4,
  },
  currentStatusLabel: {
    color: "#9CA3AF",
    fontSize: 12,
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  currentStatusValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  changeStatusButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    gap: 4,
  },
  changeStatusText: {
    color: "white",
    fontSize: 14,
    fontWeight: "500",
  },

  // Description
  description: {
    color: "#D1D5DB",
    fontSize: 14,
    lineHeight: 20,
  },
});