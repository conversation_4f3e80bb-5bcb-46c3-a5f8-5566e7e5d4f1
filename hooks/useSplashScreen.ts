import { useState, useEffect } from 'react';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export function useSplashScreen() {
  const [isAppReady, setIsAppReady] = useState(false);
  const [showCustomSplash, setShowCustomSplash] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);

  useEffect(() => {
    async function prepare() {
      try {
        // Simulate loading progress
        setLoadingProgress(20);

        // Pre-load fonts if needed
        await Font.loadAsync({
          // Add any custom fonts here if you have them
        });
        setLoadingProgress(60);

        // Simulate API initialization or other async operations
        await new Promise(resolve => setTimeout(resolve, 800));
        setLoadingProgress(100);

        // Small delay to show 100% progress
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (e) {
        console.warn('Error during app initialization:', e);
      } finally {
        // Tell the application to render
        setIsAppReady(true);
      }
    }

    prepare();
  }, []);

  const onCustomSplashFinish = () => {
    setShowCustomSplash(false);
  };

  return {
    isAppReady,
    showCustomSplash,
    onCustomSplashFinish,
    loadingProgress,
  };
}
