# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

ALIAS Component Gallery is a React Native/Expo application (SDK 53) built with TypeScript. It's a mobile-first component gallery app with web support, currently functional but with collaborative features temporarily disabled due to Jazz framework React Native compatibility issues.

## Common Development Commands

```bash
# Install dependencies
npm install

# Start Expo development server
npm start

# Run on specific platforms
npm run android  # Run on Android device/emulator
npm run ios      # Run on iOS device/simulator
npm run web      # Run in web browser

# Start Convex backend development
npm run dev      # Runs: npx convex dev

# Lint the code
npm run lint     # Runs: expo lint

# Clear Metro cache if experiencing issues
npx expo start -c

# Diagnose common Expo issues
npx expo doctor
```

## Architecture

### Core Structure
- `/app` - Expo Router pages with file-based navigation
  - `/(tabs)` - Tab navigation screens (index, learn, ops, profile, projects, toolbox)
  - `/project` - Dynamic project routes
  - `/toolbox` - Toolbox-specific screens
- `/components` - React Native components including ComponentGallery
- `/convex` - Backend functions and schema definitions
  - Real-time database with auth, projects, activity tracking
  - Deployment: `resolute-bandicoot-771`
- `/lib/jazz` - Jazz framework integration (currently disabled)
- `/assets` - Static assets including fonts and splash screen

### Key Technologies
- **Frontend**: React Native 0.79.5 with Expo SDK 53
- **Navigation**: Expo Router with React Navigation tabs
- **Backend**: Convex for real-time database
- **Styling**: Custom React Native styles with blur effects
- **State**: Local state management (Jazz collaborative features disabled)
- **TypeScript**: Strict mode enabled

### Environment Variables
```bash
# Required in .env.local
CONVEX_DEPLOYMENT=dev:resolute-bandicoot-771
EXPO_PUBLIC_CONVEX_URL=https://resolute-bandicoot-771.convex.cloud
```

## Current Status

### Working Features
- Full Component Gallery with CRUD operations
- Search and category filtering
- Professional UI with liquid glass navigation bar
- Custom splash screen with animations
- Haptic feedback on mobile
- Modal-based component editor

### Temporarily Disabled (Jazz Compatibility)
- Real-time collaboration
- Live cursors and presence
- Offline-first capabilities
- Shared workspaces

The app is fully functional as a single-user component gallery. Jazz collaborative features are commented out in:
- `components/ComponentGallery.tsx`
- `app/(tabs)/projects.tsx`
- `app/(tabs)/index.tsx`
- `app/_layout.tsx`

See `JAZZ_INTEGRATION_STATUS.md` for detailed status and next steps.