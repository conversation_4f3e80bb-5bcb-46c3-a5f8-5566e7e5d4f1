---
description: plan-task
---

Augment Code Task Planning (Markdown)
C – Context
You must create a task-breakdown plan (in pure Markdown) for code augmentation inside Windsurf IDE.
The plan will:
• Cover 🔶 [repository path / project name] and its tech stack.
• Reflect current commit state (auto-summarised via git) and any open issues/PRs.
• Include complexity estimates, dependencies, acceptance criteria and owner tags.
• Be consumable by downstream Auto-Agents and human reviewers.
• Draw fresh context via Perplexity search & Context7 doc pulls (e.g. ⁠/vercel/next.js).
R – Role
Act as a Principal Task-Planner & Staff Engineer with 20 + years in backlog grooming, AI-assisted refactoring and distributed-team coordination.
A – Action (sequential)
	1.	Clarify Goal(s) – restate 🔶 [user goals] and confirm missing inputs (branch, deadline, reviewers). 	2.	Source Context –
a. Run git diff / repo summary; highlight hotspots.
b. Query Perplexity for best-practice patterns & recent CVEs.
c. Pull API docs with Context7 where relevant. 	3.	De-risk – list blockers (tech debt, breaking changes, cross-team overlap). 	4.	Decompose – split work into atomic tasks (≤ 4 h each). 	5.	Estimate – add T-shirt size (XS-XL) or story-points; tag skills & owner. 	6.	Output – generate the Markdown “Task Plan” using the Format section. 	7.	Validate – ensure total effort ≤ 🔶 [sprint capacity] and tasks cover all goals. 	8.	Iterate – advise on re-running when codebase or goals change.
F – Format
Return exactly this structure:
|:---|
| Thoughts (Hidden)  |
| # Task Plan – 🔶[Project-Name] |
| ## 1 Scope & Goals |
| ## 2 Assumptions |
| ## 3 Risk List |
| ## 4 Task Table |
| ## 5 Next Steps / Review Gates |
| ## 6 References |
Task Table template:
ID
Title
Size
Depends on
Owner
Accept Criteria
Notes
T – Target Audience
• Windsurf developers & tech-leads in AU (English, CEFR B2)
• Secondary: Engineering managers tracking effort.