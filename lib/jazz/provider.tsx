import React, { ReactNode } from 'react';
import { JazzReactProvider, useAccount, useCoState } from 'jazz-tools/expo';
import { Platform } from 'react-native';
import { JazzAccount, JazzUserProfile } from './schemas';

// Jazz configuration - React Native compatible
const JAZZ_CONFIG = {
  // Use Jazz Cloud for now - can switch to self-hosted later
  sync: { peer: "wss://cloud.jazz.tools" },
  // Use memory storage for React Native (no indexedDB available)
  storage: Platform.OS === 'web' ? "indexedDB" as const : "memory" as const,
};

interface JazzProviderProps {
  children: ReactNode;
}

export function JazzProvider({ children }: JazzProviderProps) {
  return (
    <JazzReactProvider
      sync={JAZZ_CONFIG.sync}
      storage={JAZZ_CONFIG.storage}
      AccountSchema={JazzAccount}
    >
      {children}
    </JazzReactProvider>
  );
}

// Custom hook to get the current Jazz account
export function useJazzAccount() {
  return useAccount<JazzAccount>();
}

// Custom hook to get user profile
export function useJazzProfile() {
  const account = useJazzAccount();
  return useCoState(JazzUserProfile, account?.profile?.id);
}

// Custom hook for authentication state
export function useJazzAuth() {
  // Temporarily return fallback data for React Native compatibility
  // TODO: Re-enable Jazz when React Native compatibility is resolved

  return {
    isAuthenticated: false, // Set to false for now
    account: null,
    profile: null,
    isLoading: false,
  };

  // Original Jazz implementation (commented out for React Native compatibility)
  /*
  const account = useJazzAccount();

  return {
    isAuthenticated: !!account,
    account,
    profile: account?.profile,
    isLoading: account === undefined,
  };
  */
}

// Helper function to create a new user profile
export async function createJazzProfile(
  account: JazzAccount,
  profileData: {
    name: string;
    avatar?: string;
    bio?: string;
  }
) {
  if (!account) throw new Error('No Jazz account available');
  
  const profile = JazzUserProfile.create({
    name: profileData.name,
    avatar: profileData.avatar,
    bio: profileData.bio,
    preferences: {
      theme: 'auto',
      notifications: true,
    },
  }, account);
  
  // Link profile to account
  account.profile = profile;
  
  return profile;
}

// Helper function to update user profile
export async function updateJazzProfile(
  profile: JazzUserProfile,
  updates: Partial<{
    name: string;
    avatar: string;
    bio: string;
    preferences: {
      theme: 'light' | 'dark' | 'auto';
      notifications: boolean;
    };
  }>
) {
  if (!profile) throw new Error('No profile available');
  
  Object.assign(profile, updates);
  
  return profile;
}

// Helper to get user's color for presence/cursors
export function getUserPresenceColor(userId: string): string {
  const colors = [
    '#0099FF', // Primary blue
    '#00C896', // Green
    '#9B59FF', // Purple
    '#FF6B6B', // Red
    '#FFB800', // Orange
    '#00D4FF', // Cyan
    '#FF4081', // Pink
    '#7C4DFF', // Indigo
  ];
  
  // Generate consistent color based on user ID
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
}

// Helper to format timestamps
export function formatJazzTimestamp(timestamp: string): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);
  
  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString();
}

// Helper to generate unique IDs
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

export default JazzProvider;
